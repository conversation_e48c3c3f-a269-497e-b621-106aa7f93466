from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime
# from . import file_api, task_api, visualization_api, health_api, pipe_api
from app.core.dependencies import get_templates, get_db
from app.models import RawFile, ParquetFile
from app.models.enums import ProcessStatus
# from app.core.config import templates
from app.core import logger, templates
from .file_api import router as file_router
from .task_api import router as task_router
from .visualization_api import router as visualization_router
from .health_api import router as health_router
from .pipe_api import pipe_router
from .combustion_api import router as combustion_router

# 创建主路由
api_router = APIRouter()

# 包含子路由
api_router.include_router(file_router, prefix="/files", tags=["files"])
api_router.include_router(task_router, prefix="/tasks", tags=["任务处理"])
api_router.include_router(visualization_router,
                          prefix="/visualizations", tags=["数据可视化"])
api_router.include_router(health_router, prefix="/health", tags=["健康检查"])
api_router.include_router(pipe_router, prefix="/pipe", tags=["管道计算"])
api_router.include_router(
    combustion_router, prefix="/combustion", tags=["燃烧计算"])

# 页面路由


@api_router.get("/", response_class=HTMLResponse)
async def index_page(request: Request):
    """首页"""
    return templates.TemplateResponse("index.html", {"request": request})


@api_router.get("/pipe-calculator", response_class=HTMLResponse)
async def pipe_calculator(request: Request):
    """管道计算选型工具页面"""
    logger.info("访问管道计算选型工具页面")
    try:
        return templates.TemplateResponse("pipe_calculator.html", {"request": request})
    except Exception as e:
        logger.error(f"渲染模板时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/combustion-calculator", response_class=HTMLResponse)
async def combustion_calculator_page(request: Request):
    """燃烧计算器页面"""
    logger.info("访问燃烧计算器页面")
    try:
        return templates.TemplateResponse("combustion/calculator.html", {"request": request})
    except Exception as e:
        logger.error(f"渲染燃烧计算器模板时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/upload", response_class=HTMLResponse)
async def upload_page(request: Request):
    """上传页面"""
    return templates.TemplateResponse("upload.html", {"request": request})


@api_router.get("/tasks", response_class=HTMLResponse)
async def tasks_page(request: Request):
    """任务页面"""
    return templates.TemplateResponse("tasks.html", {"request": request})


@api_router.get("/system", response_class=HTMLResponse)
async def system_page(request: Request):
    """系统页面"""
    return templates.TemplateResponse("system.html", {"request": request})

@api_router.get("/status", response_class=HTMLResponse)
async def status_page(request: Request, db: Session = Depends(get_db)):
    """状态页面，显示系统统计信息"""

    # 获取原始文件统计
    raw_files = db.query(RawFile).all()
    raw_count = len(raw_files)
    raw_size = sum(
        f.file_size for f in raw_files if f.file_size is not None) or 0

    # 获取Parquet文件统计
    parquet_files = db.query(ParquetFile).all()
    parquet_count = len(parquet_files)
    parquet_size = sum(
        f.file_size for f in parquet_files if f.file_size is not None) or 0

    # 计算压缩率
    compression_ratio = 0
    if raw_size > 0 and parquet_size > 0:
        compression_ratio = (1 - (parquet_size / raw_size)) * 100

    # 计算状态统计
    status_count = {
        "pending": db.query(RawFile).filter(RawFile.status == ProcessStatus.PENDING).count(),
        "processing": db.query(RawFile).filter(RawFile.status == ProcessStatus.PROCESSING).count(),
        "completed": db.query(RawFile).filter(RawFile.status == ProcessStatus.COMPLETED).count(),
        "error": db.query(RawFile).filter(RawFile.status == ProcessStatus.ERROR).count()
    }

    # 计算各状态大小统计
    status_size = {
        "pending": sum(f.file_size for f in db.query(RawFile).filter(
            RawFile.status == ProcessStatus.PENDING).all() if f.file_size is not None) or 0,
        "processing": sum(f.file_size for f in db.query(RawFile).filter(
            RawFile.status == ProcessStatus.PROCESSING).all() if f.file_size is not None) or 0,
        "completed": sum(f.file_size for f in db.query(RawFile).filter(
            RawFile.status == ProcessStatus.COMPLETED).all() if f.file_size is not None) or 0,
        "error": sum(f.file_size for f in db.query(RawFile).filter(
            RawFile.status == ProcessStatus.ERROR).all() if f.file_size is not None) or 0
    }

    # 计算今日上传文件数
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    files_today = db.query(RawFile).filter(
        RawFile.created_at >= today_start).count()

    # 总记录数和数据段数
    total_records = sum(
        f.total_records for f in raw_files if f.total_records is not None) or 0
    total_segments = db.query(RawFile).join(RawFile.parquet_files).count() or 0

    # 构建传递给模板的状态字典
    stats = {
        "total_files": raw_count,
        "files_today": files_today,  # 添加今日文件数
        "raw_files": raw_count,
        "raw_size": raw_size,
        "parquet_files": parquet_count,
        "parquet_size": parquet_size,
        "total_size": raw_size + parquet_size,
        "compression_ratio": compression_ratio,
        "status_count": status_count,
        "status_size": status_size,
        "total_records": total_records,
        "total_segments": total_segments,
        "timestamp": datetime.now()
    }

    return templates.TemplateResponse(
        "status.html",
        {"request": request, "stats": stats}
    )


@api_router.get("/files", response_class=HTMLResponse)
async def files_page(request: Request, db: Session = Depends(get_db)):
    """文件页面"""
    return templates.TemplateResponse("files.html", {"request": request})


@api_router.get("/test-material-stress", response_class=HTMLResponse)
async def test_material_stress_page(request: Request):
    """材质许用应力测试页面"""
    logger.info("访问材质许用应力测试页面")
    try:
        return templates.TemplateResponse("test_material_stress.html", {"request": request})
    except Exception as e:
        logger.error(f"渲染材质许用应力测试模板时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/test-diameter-calculation", response_class=HTMLResponse)
async def test_diameter_calculation_page(request: Request):
    """内径计算验证测试页面"""
    logger.info("访问内径计算验证测试页面")
    try:
        return templates.TemplateResponse("test_diameter_calculation.html", {"request": request})
    except Exception as e:
        logger.error(f"渲染内径计算验证测试模板时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
