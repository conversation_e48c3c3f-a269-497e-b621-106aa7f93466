// 1. 常量与配置
const ERROR_MESSAGES = {
    CALC_FAILED: '计算失败',
    DIAMETER_INVALID: '内径小于零',
    // DIAMETER_ZERO: '内径为零',
    UNKNOWN_MEDIUM: '未知介质',
    PRESSURE_INVALID: '压力（绝压）必须大于零',
    TEMPERATURE_INVALID: '温度（K）必须大于零',
    FLOW_INVALID: '流量须大于零',
    NO_OUTER_DIAMETER: '未找到合适的标准外径，无法计算厚度'
};
const MEDIUM_CONFIG = {
    '压缩空气': { type: 'gas', Rg: 287.1, density: NaN, velocity: 50 },
    '水': { type: 'liquid', density: 1000, velocity: 3 },
    '航空煤油': { type: 'liquid', density: 785, velocity: 3 }
};

// 材质许用应力数据表 (MPa) - 按温度(°C)分类
const MATERIAL_ALLOWABLE_STRESS = {
    '20G': {
        // 20G钢材许用应力数据 (根据GB20801.2标准表格更新)
        standard: 'GB5310',
        maxTemperature: 593,  // 最高使用温度 °C
        description: '高压锅炉用无缝钢管',
        temperatures: [20, 40, 65, 100, 150, 200, 250, 300, 325, 350, 375, 400, 425, 450, 475, 500, 525, 550, 575],
        stresses: [137, 137, 137, 137, 137, 137, 132, 126, 122, 118, 113, 95.1, 79.5, 62.6, 45.0, 31.7, 21.4, 14.2, 9.40]
    },
    '16MnR': {
        // 16MnR钢材许用应力数据
        standard: 'GB 6654',
        maxTemperature: 475,  // 最高使用温度 °C
        description: '压力容器用钢板',
        temperatures: [20, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500],
        stresses: [170, 170, 170, 170, 169, 163, 152, 133, 105, 72, 40]
    },
    '15CrMoR': {
        // 15CrMoR钢材许用应力数据
        standard: 'GB 713',
        maxTemperature: 580,  // 最高使用温度 °C
        description: '压力容器用钢板',
        temperatures: [20, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600],
        stresses: [170, 170, 170, 170, 170, 170, 170, 170, 167, 160, 147, 127, 98]
    },
    '12Cr1MoVR': {
        // 12Cr1MoVR钢材许用应力数据
        standard: 'GB 713',
        maxTemperature: 600,  // 最高使用温度 °C
        description: '压力容器用钢板',
        temperatures: [20, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600],
        stresses: [170, 170, 170, 170, 170, 170, 170, 170, 170, 170, 167, 147, 118]
    },
    '321': {
        // 321不锈钢许用应力数据 (06Cr18Ni11Ti)
        standard: 'GB/T 14976',
        maxTemperature: 816,  // 最高使用温度 °C
        description: '流体输送用不锈钢无缝钢管',
        temperatures: [40, 65, 100, 150, 200, 250, 300, 325, 350, 375, 400, 425, 450, 475, 500, 525, 550, 575, 600, 625, 650, 675, 700, 725, 750, 775, 800, 816],
        stresses: [138, 138, 138, 138, 138, 135, 128, 125, 123, 120, 119, 117, 116, 114, 113, 112, 88.7, 59.2, 44.0, 39.2, 24.5, 18.3, 12.5, 8.49, 6.19, 4.28, 2.75, 2.07]
    },
    '304': {
        // 304不锈钢许用应力数据 (06Cr19Ni10)
        standard: 'GB/T 14976',
        maxTemperature: 816,  // 最高使用温度 °C
        description: '流体输送用不锈钢无缝钢管',
        temperatures: [40, 65, 100, 150, 200, 250, 300, 325, 350, 375, 400, 425, 450, 475, 500, 525, 550, 575, 600, 625, 650, 675, 700, 725, 750, 775, 800, 816],
        stresses: [138, 138, 138, 129, 122, 116, 113, 111, 109, 107, 106, 103, 101, 99, 97, 95, 78.9, 63.4, 61.6, 41.6, 32.9, 26.5, 21.2, 17.4, 14.1, 11.2, 9.65, 9.65]
    },
    '316L': {
        // 316L不锈钢许用应力数据
        standard: 'GB/T 14976',
        maxTemperature: 800,  // 最高使用温度 °C
        description: '流体输送用不锈钢无缝钢管',
        temperatures: [20, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700],
        stresses: [138, 138, 138, 138, 138, 138, 138, 138, 138, 138, 138, 138, 138, 138, 138]
    }
};

/**
 * 根据材质和温度获取许用应力
 * @param {string} material - 材质名称
 * @param {number} temperature - 温度(°C)
 * @returns {number} 许用应力(MPa)
 */
function getAllowableStress(material, temperature) {
    const materialData = MATERIAL_ALLOWABLE_STRESS[material];
    if (!materialData) {
        console.warn(`未找到材质 ${material} 的许用应力数据，使用默认值138 MPa`);
        return 138; // 默认值
    }

    const { temperatures, stresses, maxTemperature, standard, description } = materialData;

    console.log('****许用应力查询过程****');
    console.log(`材质：${material} (${description})`);
    console.log(`标准：${standard}`);
    console.log(`最高使用温度：${maxTemperature}°C`);
    console.log(`查询温度：${temperature}°C`);

    // 检查温度是否超过最高使用温度
    if (temperature > maxTemperature) {
        console.warn(`⚠️ 警告：温度${temperature}°C超过材质${material}的最高使用温度${maxTemperature}°C！`);
        console.warn(`建议选择其他材质或降低使用温度。`);
    }

    // 如果温度低于最低温度，使用最低温度的应力值
    if (temperature <= temperatures[0]) {
        console.log(`温度低于最低温度${temperatures[0]}°C，使用最低温度应力值：${stresses[0]} MPa`);
        return stresses[0];
    }

    // 如果温度高于最高温度，使用最高温度的应力值
    if (temperature >= temperatures[temperatures.length - 1]) {
        console.log(`温度高于最高温度${temperatures[temperatures.length - 1]}°C，使用最高温度应力值：${stresses[stresses.length - 1]} MPa`);
        return stresses[stresses.length - 1];
    }

    // 线性插值计算中间温度的许用应力
    for (let i = 0; i < temperatures.length - 1; i++) {
        if (temperature >= temperatures[i] && temperature <= temperatures[i + 1]) {
            const t1 = temperatures[i];
            const t2 = temperatures[i + 1];
            const s1 = stresses[i];
            const s2 = stresses[i + 1];

            // 线性插值公式: s = s1 + (s2 - s1) * (t - t1) / (t2 - t1)
            const stress = s1 + (s2 - s1) * (temperature - t1) / (t2 - t1);
            const finalStress = Math.round(stress * 100) / 100; // 保留两位小数

            console.log(`温度在${t1}°C和${t2}°C之间，进行线性插值：`);
            console.log(`  T1=${t1}°C, S1=${s1} MPa`);
            console.log(`  T2=${t2}°C, S2=${s2} MPa`);
            console.log(`  插值结果：${finalStress} MPa`);

            return finalStress;
        }
    }

    return 138; // 默认值
}

/**
 * 获取可用的材质列表
 * @returns {Array} 材质名称数组
 */
function getAvailableMaterials() {
    return Object.keys(MATERIAL_ALLOWABLE_STRESS);
}

/**
 * 获取材质的详细信息
 * @param {string} material - 材质名称
 * @returns {Object} 材质详细信息
 */
function getMaterialInfo(material) {
    const materialData = MATERIAL_ALLOWABLE_STRESS[material];
    if (!materialData) {
        return null;
    }

    return {
        name: material,
        standard: materialData.standard,
        maxTemperature: materialData.maxTemperature,
        description: materialData.description,
        temperatureRange: `${materialData.temperatures[0]}°C - ${materialData.temperatures[materialData.temperatures.length - 1]}°C`
    };
}

/**
 * 获取指定材质的温度范围
 * @param {string} material - 材质名称
 * @returns {Object} {min: 最低温度, max: 最高温度} 或 null
 */
function getMaterialTemperatureRange(material) {
    const materialData = MATERIAL_ALLOWABLE_STRESS[material];
    if (!materialData) return null;

    const temps = materialData.temperatures;
    return {
        min: temps[0],
        max: temps[temps.length - 1]
    };
}

// 管道外径表 (数据来源: 提供的图片)
const pipeOuterDiameters = {
    '10': { A: 17.2, B: 14 },
    '15': { A: 21.3, B: 18 },
    '20': { A: 26.9, B: 25 },
    '25': { A: 33.7, B: 32 },
    '32': { A: 42.4, B: 38 },
    '40': { A: 48.3, B: 45 },
    '50': { A: 60.3, B: 57 },
    '65': { A: 76.1, B: 76 },
    '80': { A: 88.9, B: 89 },
    '100': { A: 114.3, B: 108 },
    '125': { A: 139.7, B: 133 },
    '150': { A: 168.3, B: 159 },
    '200': { A: 219.1, B: 219 },
    '250': { A: 273, B: 273 },
    '300': { A: 323.9, B: 325 },
    '350': { A: 355.6, B: 377 },
    '400': { A: 406.4, B: 426 },
    '450': { A: 457, B: 480 },
    '500': { A: 508, B: 530 },
    '600': { A: 610, B: 630 },
    '700': { A: 711, B: 720 },
    '800': { A: 813, B: 820 },
    '900': { A: 914, B: 920 },
    '1000': { A: 1016, B: 1020 },
    '1200': { A: 1219, B: 1220 },
    '1400': { A: 1422, B: 1420 },
    '1600': { A: 1626, B: 1620 },
    '1800': { A: 1829, B: 1820 },
    '2000': { A: 2032, B: 2020 },
};

// 2. 错误处理
function handleCalcError(type, detail = '') {
    let msg = ERROR_MESSAGES[type] || ERROR_MESSAGES.CALC_FAILED;
    if (detail) msg += '：' + detail;
    return msg;
}



// 单位换算函数
function convertPressureToPa(pressure, pressureUnit) {
    if (pressureUnit === 'MPa') {
        return pressure * 1e6;
    } else if (pressureUnit === 'kPa') {
        return pressure * 1e3;
    } else if (pressureUnit === 'bar') {
        return pressure * 1e5;
    }
    // 默认Pa
    return pressure;
}
function convertTemperatureToK(temperature, temperatureUnit) {
    if (temperatureUnit === 'C') {
        return temperature + 273.15;
    }
    return temperature;
}
// 4. 流量单位转换
function convertFlowToKgS(flow, flowUnit, medium, pressure, temperature) {
    const config = MEDIUM_CONFIG[medium];
    if (!config) throw new Error(handleCalcError('UNKNOWN_MEDIUM'));
    let density = config.density || 1;
    // 这里不再重复计算气体密度，直接用config.density
    switch (flowUnit) {
        case 'kg/h': return flow / 3600;
        case 'kg/s': return flow;
        case 'm3/h': return (flow / 3600) * density;
        case 'm3/s': return flow * density;
        default: throw new Error(handleCalcError('FLOW_INVALID'));
    }
}
// 单位换算整合
function convertUnits({ pressure, pressureUnit, temperature, temperatureUnit, flow, flowUnit, medium }) {
    const stdPressure = convertPressureToPa(pressure, pressureUnit);
    const stdTemperature = convertTemperatureToK(temperature, temperatureUnit);
    // 先处理气体密度
    const config = MEDIUM_CONFIG[medium];
    if (config && config.type === 'gas') {
        const Rg = config.Rg;
        const absP = stdPressure;
        const tempK = stdTemperature;
        config.density = absP / (Rg * tempK);
    }
    const stdFlow = convertFlowToKgS(flow, flowUnit, medium, stdPressure, stdTemperature);
    return { pressure: stdPressure, temperature: stdTemperature, flow: stdFlow };
}
// 3. 数据验证
function validateRowData({ medium, pressure, temperature, flow }) {
    if (!MEDIUM_CONFIG[medium]) throw new Error(handleCalcError('UNKNOWN_MEDIUM'));
    if (isNaN(pressure) || pressure <= 0) throw new Error(handleCalcError('PRESSURE_INVALID'));
    // 温度校验：根据单位判断绝对温度
    const temperatureUnit = document.getElementById('temperatureUnit').value;
    let absTemperature = temperature;
    if (temperatureUnit === 'C') {
        absTemperature = temperature + 273.15;
    }
    if (isNaN(temperature) || absTemperature <= 0) throw new Error(handleCalcError('TEMPERATURE_INVALID'));
    if (isNaN(flow) || flow <= 0) throw new Error(handleCalcError('FLOW_INVALID'));
}

// 5. 内径计算
function calculatePipeInnerDiameter({ medium, flow_kg_s }) {
    const config = MEDIUM_CONFIG[medium];
    if (!config) throw new Error(handleCalcError('UNKNOWN_MEDIUM'));

    if (config.type === 'gas') {
        const density = config.density; // 已在convertUnits中计算好的密度
        const velocity = config.velocity; // 推荐流速 m/s

        // 计算体积流量 Q_v = 质量流量 / 密度 (m³/s)
        const Q_v = flow_kg_s / density;

        // 使用连续性方程计算内径: d = sqrt((4 * Q_v) / (π * v))
        const d = Math.sqrt((4 * Q_v) / (Math.PI * velocity));

        // 详细打印计算过程
        console.log('****气体内径计算过程****');
        console.log(`介质：${medium}`);
        console.log(`质量流量：${flow_kg_s} kg/s`);
        console.log(`密度：${density.toFixed(3)} kg/m³`);
        console.log(`体积流量：${Q_v.toFixed(6)} m³/s`);
        console.log(`推荐流速：${velocity} m/s`);
        console.log(`计算内径：${(d * 1000).toFixed(2)} mm`);

        return (d * 1000).toFixed(2);
    } else if (config.type === 'liquid') {
        const density = config.density;
        const velocity = config.velocity;

        // 计算体积流量 Q_v = 质量流量 / 密度 (m³/s)
        const Q_v = flow_kg_s / density;

        // 使用连续性方程计算内径: d = sqrt((4 * Q_v) / (π * v))
        const d = Math.sqrt((4 * Q_v) / (Math.PI * velocity));

        // 详细打印计算过程
        console.log('****液体内径计算过程****');
        console.log(`介质：${medium}`);
        console.log(`质量流量：${flow_kg_s} kg/s`);
        console.log(`密度：${density} kg/m³`);
        console.log(`体积流量：${Q_v.toFixed(6)} m³/s`);
        console.log(`推荐流速：${velocity} m/s`);
        console.log(`计算内径：${(d * 1000).toFixed(2)} mm`);

        return (d * 1000).toFixed(2);
    }
    throw new Error(handleCalcError('UNKNOWN_MEDIUM'));
}

/**
 * 计算直管厚度
 * @param {Object} params
 *   - P: 设计压力（MPa）
 *   - D: 外径（mm）
 *   - S: 许用应力（MPa）
 *   - phi: 焊缝系数（默认1）
 *   - W: 强度系数（默认1）
 *   - Y: 厚度系数（默认0）
 * @returns {number} 厚度t（mm）
 */
function calculatePipeThickness({ P, D, S, phi = 1, W = 1, Y = 0.4 }) {
    const denominator = 2 * (S * phi * W + P * Y);
    // if (denominator <= 0) return NaN;
    return (P * D) / denominator;
}

/**
 * 计算管道规格全流程
 * @param {Object} params - { medium, pressure, temperature, flow_kg_s, material }
 * @returns {Object} - { calcInnerDiameter, allowableStress, calcThickness, C, designThickness, nominalThickness, C1, C1_std, outerDiameter, pipeInnerDiameter }
 */
function calculatePipeFullSpec({ medium, pressure, temperature, flow_kg_s, material = '20G', pipeSeries = 'A' }) {
    // 1）计算内径
    const calcInnerDiameter = parseFloat(calculatePipeInnerDiameter({ medium, flow_kg_s }));
    // 2）根据材质和温度查询许用应力
    // 将温度从K转换为°C用于查表
    const temperatureC = temperature - 273.15;
    const allowableStress = getAllowableStress(material, temperatureC);
    // 3）计算厚度（集成公式）
    // 选取最小满足条件的标准外径
    let outerDiameter = null;
    let minDiff = Infinity;
    for (const key in pipeOuterDiameters) {
        const od = pipeOuterDiameters[key][pipeSeries];
        if (od > calcInnerDiameter && (od - calcInnerDiameter) < minDiff) {
            minDiff = od - calcInnerDiameter;
            outerDiameter = od;
        }
    }
    if (outerDiameter == null) {
        throw new Error(handleCalcError('NO_OUTER_DIAMETER'));
    }
    // 计算名义厚度及相关参数的复用函数
    function calcSpecParams(od) {
        let calcThickness = calculatePipeThickness({ P: P_MPa, D: od, S: allowableStress, phi: 1, W: 1, Y: 0 });
        let designThickness = calcThickness + C;
        let nominalThickness = Math.ceil(designThickness / (1 - 0.125));
        let C1_std = nominalThickness * 0.125;
        let C1 = nominalThickness - designThickness;
        while (C1 <= C1_std) {
            nominalThickness += 1;
            C1_std = nominalThickness * 0.125;
            C1 = nominalThickness - designThickness;
        }
        let pipeInnerDiameter = od - 2 * nominalThickness;
        return { calcThickness, designThickness, nominalThickness, C1, C1_std, pipeInnerDiameter };
    }
    // 计算初始规格
    const C = 1.5;
    const P_MPa = pressure / 1e6;
    let { calcThickness, designThickness, nominalThickness, C1, C1_std, pipeInnerDiameter } = calcSpecParams(outerDiameter);
    // 规格提升循环
    const odKeys = Object.keys(pipeOuterDiameters).sort((a, b) => pipeOuterDiameters[a][pipeSeries] - pipeOuterDiameters[b][pipeSeries]);
    while (pipeInnerDiameter < calcInnerDiameter) {
        let found = false;
        for (let i = 0; i < odKeys.length; i++) {
            const nextOd = pipeOuterDiameters[odKeys[i]][pipeSeries];
            if (nextOd > outerDiameter) {
                outerDiameter = nextOd;
                ({ calcThickness, designThickness, nominalThickness, C1, C1_std, pipeInnerDiameter } = calcSpecParams(outerDiameter));
                found = true;
                break;
            }
        }
        if (!found) {
            throw new Error(handleCalcError('NO_OUTER_DIAMETER'));
        }
    }
    // 统一结果对象
    const result = {
        diameter_mm: calcInnerDiameter, // 计算内径
        allowableStress, // 许用应力
        calcThickness, // 计算厚度
        C, // 厚度附加量
        designThickness, // 设计厚度
        nominalThickness, // 名义厚度
        C1, // 材料厚度负偏差
        C1_std, // 材料厚度负偏差标准值
        outerDiameter, // 外径
        pipeInnerDiameter // 管道规格内径
    };
    // 简化打印
    console.log('****管道规格计算结果****');
    const unitMap = {
        diameter_mm: 'mm',
        allowableStress: 'MPa',
        calcThickness: 'mm',
        C: 'mm',
        designThickness: 'mm',
        nominalThickness: 'mm',
        C1: 'mm',
        C1_std: 'mm',
        outerDiameter: 'mm',
        pipeInnerDiameter: 'mm'
    };
    for (const key in result) {
        if (result.hasOwnProperty(key)) {
            console.log(`${key}:`, result[key], unitMap[key] || '');
        }
    }
    // 返回结果对象
    return result;
}

// 6. 流速计算
/**
 * 计算管道流速
 * @param {Object} params - { medium, pressure, temperature, flow_kg_s, diameter_mm }
 *   - pressure: Pa（已换算）
 *   - temperature: K（已换算）
 *   - diameter_mm: mm（已换算，通常为最终内径）
 *   - flow_kg_s: kg/s
 * @returns {string} 流速（m/s，保留两位小数）
 */
function calculateVelocity({ medium, pressure, temperature, flow_kg_s, diameter_mm }) {
    const config = MEDIUM_CONFIG[medium];
    if (!config) throw new Error(handleCalcError('UNKNOWN_MEDIUM'));
    let density = config.density || 1;
    // 这里不再重复计算气体密度，直接用config.density
    // diameter_mm为mm，需转为m
    const d_m = diameter_mm / 1000;
    const area = Math.PI * Math.pow(d_m, 2) / 4;
    if (area <= 0) return handleCalcError('DIAMETER_INVALID');
    const Qv = flow_kg_s / density;
    const velocity = Qv / area;
    // 详细打印中间变量
    console.log('****流速计算过程****');
    console.log(`d_m（内径，m）：${d_m}`);
    console.log(`area（截面积，m²）：${area}`);
    console.log(`density（密度，kg/m³）：${density}`);
    console.log(`Qv（体积流量，m³/s）：${Qv}`);
    console.log(`velocity（流速，m/s）：${velocity}`);
    return velocity.toFixed(2);
}


// 7. 主计算流程
function calculateRow(row) {
    try {
        // 获取输入，兼容input和readonly
        function getCellValue(cell) {
            const input = cell.querySelector('input');
            return input ? input.value.trim() : cell.textContent.trim();
        }
        const medium = getCellValue(row.cells[1]);
        const material = getCellValue(row.cells[2]); // 获取材质信息
        let pressure = parseFloat(getCellValue(row.cells[3]));
        let temperature = parseFloat(getCellValue(row.cells[4]));
        let flow = parseFloat(getCellValue(row.cells[5]));
        const flowUnit = document.getElementById('flowUnit').value;
        const pressureUnit = document.getElementById('pressureUnit').value;
        const temperatureUnit = document.getElementById('temperatureUnit').value;
        // 1. 单位换算整合
        const std = convertUnits({ pressure, pressureUnit, temperature, temperatureUnit, flow, flowUnit, medium });
        // 2. 数据验证（传入已换算后的绝对值）
        validateRowData({ medium, pressure: std.pressure, temperature: std.temperature, flow: std.flow });
        const pipeSeries = document.getElementById('pipeSeries') ? document.getElementById('pipeSeries').value : 'A';
        // 3. 管道规格计算（传入材质参数）
        const spec = calculatePipeFullSpec({ medium, pressure: std.pressure, temperature: std.temperature, flow_kg_s: std.flow, material, pipeSeries });
        const OD_th = `φ${spec.outerDiameter}X${spec.nominalThickness}`;
        // 4. 流速计算
        const velocity = calculateVelocity({ medium, pressure: std.pressure, temperature: std.temperature, flow_kg_s: std.flow, diameter_mm: spec.pipeInnerDiameter });
        // 5. 保温层厚度（待实现）
        const insulationThickness = '待计算';
        // 6. 更新表格
        row.cells[6].textContent = OD_th;
        row.cells[7].textContent = insulationThickness;
        row.cells[8].textContent = velocity;
        
    } catch (error) {
        row.cells[6].textContent = error.message;
        row.cells[7].textContent = handleCalcError('CALC_FAILED');
        row.cells[8].textContent = handleCalcError('CALC_FAILED');
    }
}

function setDefaultProjectName() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const defaultName = `项目1-${year}-${month}-${day}`;
    document.getElementById('projectName').value = defaultName;
}

function handlePressureStandardChange() {
    const standard = document.getElementById('pressureStandard').value;
    // 这里可以添加根据压力标准更新相关选项的逻辑
    console.log('压力标准变更为:', standard);
}

function handleMediumChange() {
    const medium = document.getElementById('medium').value;
    const velocityStandard = document.getElementById('velocityStandard');
    switch(medium) {
        case '压缩空气':
            velocityStandard.value = '50';
            break;
        case '水':
            velocityStandard.value = '3';
            break;
        case '航空煤油':
            velocityStandard.value = '3';
            break;
        default:
            velocityStandard.value = '';
    }
}

function handleVelocityChange() {
    const velocity = document.getElementById('velocityStandard').value;
    // 这里可以添加流速变更后的处理逻辑
    console.log('流速变更为:', velocity);
}

function handleUnitChange() {
    const pressureUnit = document.getElementById('pressureUnit').value;
    const temperatureUnit = document.getElementById('temperatureUnit').value;
    const flowUnit = document.getElementById('flowUnit').value;
    // 更新表头单位
    document.getElementById('th-pressure').textContent = `压力 (${pressureUnit})`;
    document.getElementById('th-temperature').textContent = `温度 (${temperatureUnit === 'C' ? '°C' : 'K'})`;
    document.getElementById('th-flow').textContent = `流量 (${flowUnit.replace('m3', 'm³')})`;
    // 这里可以添加单位变更后的处理逻辑
    console.log('单位变更为:', { pressureUnit, temperatureUnit, flowUnit });
}

function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    const icon = section.previousElementSibling.querySelector('.section-icon');
    if (section.classList.contains('expanded')) {
        section.classList.remove('expanded');
        if (icon) icon.classList.add('collapsed');
    } else {
        section.classList.add('expanded');
        if (icon) icon.classList.remove('collapsed');
    }
}

function addRow() {
    const tbody = document.querySelector('#dataTable tbody');
    const rows = tbody.getElementsByTagName('tr');
    const rowCount = rows.length;
    const pipeNumber = 101 + rowCount;
    const medium = document.getElementById('medium').value;
    const mediumText = document.getElementById('medium').options[document.getElementById('medium').selectedIndex].text;
    const material = document.getElementById('pipeMaterial').value;
    const velocity = document.getElementById('velocityStandard').value;
    const row = document.createElement('tr');
    row.innerHTML = `
        <td class="editable"><input type="text" value="${pipeNumber}" onchange="handleCellChange(this)"></td>
        <td class="readonly">${mediumText}</td>
        <td class="readonly">${material}</td>
        <td class="editable number"><input type="number" value="0" onchange="handleCellChange(this)"></td>
        <td class="editable number"><input type="number" value="0" onchange="handleCellChange(this)"></td>
        <td class="editable number"><input type="number" value="0" onchange="handleCellChange(this)"></td>
        <td class="readonly"></td>
        <td class="readonly"></td>
        <td class="readonly">${velocity}</td>
        <td><span class="action-icon" onclick="deleteRow(this)">✕</span></td>
    `;
    tbody.appendChild(row);
}

function handleCellChange(input) {
    const row = input.closest('tr');
    calculateRow(row);
}

function deleteRow(btn) {
    const row = btn.closest('tr');
    if (row) row.remove();
}

function calculate() {
    const tbody = document.querySelector('#dataTable tbody');
    const rows = tbody.getElementsByTagName('tr');
    for (let i = 0; i < rows.length; i++) {
        calculateRow(rows[i]);
    }
}

function importData() {
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];
    if (!file) {
        alert('请选择文件');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const tbody = document.querySelector('#dataTable tbody');

            // 清空现有数据
            tbody.innerHTML = '';

            // 跳过标题行，从第二行开始
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (line) {
                    const data = line.split(',');
                    if (data.length >= 6) {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td class="editable"><input type="text" value="${data[0] || ''}" onchange="handleCellChange(this)"></td>
                            <td class="readonly">${data[1] || ''}</td>
                            <td class="readonly">${data[2] || ''}</td>
                            <td class="editable number"><input type="number" value="${data[3] || '0'}" onchange="handleCellChange(this)"></td>
                            <td class="editable number"><input type="number" value="${data[4] || '0'}" onchange="handleCellChange(this)"></td>
                            <td class="editable number"><input type="number" value="${data[5] || '0'}" onchange="handleCellChange(this)"></td>
                            <td class="readonly"></td>
                            <td class="readonly"></td>
                            <td class="readonly"></td>
                            <td><span class="action-icon" onclick="deleteRow(this)">✕</span></td>
                        `;
                        tbody.appendChild(row);
                    }
                }
            }
            alert('数据导入成功');
        } catch (error) {
            alert('文件格式错误：' + error.message);
        }
    };
    reader.readAsText(file);
}

function exportReport() {
    const tbody = document.querySelector('#dataTable tbody');
    const rows = tbody.getElementsByTagName('tr');

    if (rows.length === 0) {
        alert('没有数据可导出');
        return;
    }

    let csvContent = '位号,流体介质,管道材质,压力,温度,流量,管道规格,保温层厚,流速标准\n';

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.cells;
        const rowData = [];

        for (let j = 0; j < cells.length - 1; j++) { // 排除最后一列操作列
            const cell = cells[j];
            const input = cell.querySelector('input');
            const value = input ? input.value : cell.textContent;
            rowData.push(value);
        }

        csvContent += rowData.join(',') + '\n';
    }

    // 创建下载链接
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', '管道计算报告.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

document.addEventListener('DOMContentLoaded', function() {
    setDefaultProjectName();
    handleMediumChange();
    handleUnitChange();
    // 需要展开的section
    const sections = ['basicSettingsSection', 'tableSection'];
    sections.forEach(section => {
        const element = document.getElementById(section);
        if (element) {
            element.classList.add('expanded');
            // 同步icon
            const icon = element.previousElementSibling.querySelector('.section-icon');
            if (icon) icon.classList.remove('collapsed');
        }
    });
    // 默认收起的section
    const designSection = document.getElementById('designSection');
    if (designSection) {
        designSection.classList.remove('expanded');
        const icon = designSection.previousElementSibling.querySelector('.section-icon');
        if (icon) icon.classList.add('collapsed');
    }
});

window.addRow = addRow;
window.handleCellChange = handleCellChange;
window.deleteRow = deleteRow;
window.toggleSection = toggleSection;
window.handlePressureStandardChange = handlePressureStandardChange;
window.handleMediumChange = handleMediumChange;
window.handleVelocityChange = handleVelocityChange;
window.handleUnitChange = handleUnitChange;
window.calculate = calculate;
window.importData = importData;
window.exportReport = exportReport;

// 导出材质相关函数供外部使用
window.getAllowableStress = getAllowableStress;
window.getAvailableMaterials = getAvailableMaterials;
window.getMaterialTemperatureRange = getMaterialTemperatureRange;
window.MATERIAL_ALLOWABLE_STRESS = MATERIAL_ALLOWABLE_STRESS;
