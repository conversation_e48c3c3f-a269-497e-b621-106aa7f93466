<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>材质许用应力功能测试</title>
    <!-- 引入Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        select, input {
            padding: 5px;
            margin-left: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .material-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .material-table th, .material-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .material-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .chart-container {
            position: relative;
            height: 500px;
            margin: 20px 0;
        }
        .material-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .material-checkbox {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .material-checkbox input[type="checkbox"] {
            margin: 0;
        }
        .material-checkbox label {
            margin: 0;
            width: auto;
            font-weight: normal;
            cursor: pointer;
        }
        .chart-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .chart-controls label {
            width: auto;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>材质许用应力功能测试</h1>
        
        <div class="test-section">
            <h2>1. 单个查询测试</h2>
            <div class="input-group">
                <label for="material">材质:</label>
                <select id="material">
                    <option value="20G">20G</option>
                    <option value="16MnR">16MnR</option>
                    <option value="15CrMoR">15CrMoR</option>
                    <option value="12Cr1MoVR">12Cr1MoVR</option>
                    <option value="321">321</option>
                    <option value="304">304</option>
                    <option value="316L">316L</option>
                </select>
            </div>
            <div class="input-group">
                <label for="temperature">温度(°C):</label>
                <input type="number" id="temperature" value="200" min="-50" max="800">
            </div>
            <button onclick="querySingleStress()">查询许用应力</button>
            <div id="singleResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>2. 批量测试</h2>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="showMaterialInfo()">显示材质信息</button>
            <button onclick="demonstrateQueries()">演示查询功能</button>
            <div id="batchResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>3. 材质对比表</h2>
            <div class="input-group">
                <label for="compareTemp">对比温度(°C):</label>
                <input type="number" id="compareTemp" value="300" min="0" max="800">
                <button onclick="generateComparisonTable()">生成对比表</button>
            </div>
            <div id="comparisonTable"></div>
        </div>

        <div class="test-section">
            <h2>4. 材质许用应力曲线图</h2>
            <div class="chart-controls">
                <label>温度范围:</label>
                <input type="number" id="tempMin" value="0" min="-50" max="800" placeholder="最低温度">
                <span>-</span>
                <input type="number" id="tempMax" value="600" min="0" max="850" placeholder="最高温度">
                <label>°C</label>
                <button onclick="updateChart()">更新图表</button>
                <button onclick="selectAllMaterials()">全选</button>
                <button onclick="clearAllMaterials()">清空</button>
            </div>

            <div class="material-selector" id="materialSelector">
                <!-- 材质选择器将通过JavaScript动态生成 -->
            </div>

            <div class="chart-container">
                <canvas id="stressChart"></canvas>
            </div>
        </div>
    </div>

    <!-- 引入主计算器脚本 -->
    <script src="js/pipe_calculator.js"></script>
    <!-- 引入测试脚本 -->
    <script src="js/test_material_stress.js"></script>

    <script>
        // 全局变量
        let stressChart = null;

        // 材质颜色配置
        const materialColors = {
            '20G': '#FF6384',
            '16MnR': '#36A2EB',
            '15CrMoR': '#FFCE56',
            '12Cr1MoVR': '#4BC0C0',
            '321': '#9966FF',
            '304': '#FF9F40',
            '316L': '#FF6384'
        };

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('材质许用应力测试页面已加载');
            console.log('可用材质:', getAvailableMaterials());

            // 初始化材质选择器
            initializeMaterialSelector();

            // 初始化图表
            initializeChart();
        });

        // 单个查询功能
        function querySingleStress() {
            const material = document.getElementById('material').value;
            const temperature = parseFloat(document.getElementById('temperature').value);
            const resultDiv = document.getElementById('singleResult');
            
            try {
                // 清空控制台，准备显示新的查询结果
                console.clear();
                
                const stress = getAllowableStress(material, temperature);
                const range = getMaterialTemperatureRange(material);
                
                let result = `查询结果:\n`;
                result += `材质: ${material}\n`;
                result += `温度: ${temperature}°C\n`;
                result += `许用应力: ${stress} MPa\n`;
                result += `材质温度范围: ${range.min}°C ~ ${range.max}°C\n`;
                
                if (temperature < range.min) {
                    result += `注意: 查询温度低于材质最低温度，使用最低温度的应力值\n`;
                } else if (temperature > range.max) {
                    result += `注意: 查询温度高于材质最高温度，使用最高温度的应力值\n`;
                }
                
                resultDiv.textContent = result;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        // 运行所有测试
        function runAllTests() {
            const resultDiv = document.getElementById('batchResult');
            console.clear();
            
            // 捕获控制台输出
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;
            let output = '';
            
            console.log = function(...args) {
                output += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };
            
            console.error = function(...args) {
                output += 'ERROR: ' + args.join(' ') + '\n';
                originalError.apply(console, args);
            };
            
            console.warn = function(...args) {
                output += 'WARN: ' + args.join(' ') + '\n';
                originalWarn.apply(console, args);
            };
            
            try {
                runMaterialStressTests();
                
                // 恢复原始console函数
                console.log = originalLog;
                console.error = originalError;
                console.warn = originalWarn;
                
                resultDiv.textContent = output;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                console.log = originalLog;
                console.error = originalError;
                console.warn = originalWarn;
                
                resultDiv.textContent = `测试执行错误: ${error.message}\n${output}`;
                resultDiv.style.display = 'block';
            }
        }

        // 显示材质信息
        function showMaterialInfo() {
            const resultDiv = document.getElementById('batchResult');
            console.clear();
            
            let output = '';
            const originalLog = console.log;
            console.log = function(...args) {
                output += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };
            
            showAvailableMaterials();
            
            console.log = originalLog;
            resultDiv.textContent = output;
            resultDiv.style.display = 'block';
        }

        // 演示查询功能
        function demonstrateQueries() {
            const resultDiv = document.getElementById('batchResult');
            console.clear();
            
            let output = '';
            const originalLog = console.log;
            console.log = function(...args) {
                output += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };
            
            demonstrateStressQuery();
            
            console.log = originalLog;
            resultDiv.textContent = output;
            resultDiv.style.display = 'block';
        }

        // 生成材质对比表
        function generateComparisonTable() {
            const temperature = parseFloat(document.getElementById('compareTemp').value);
            const tableDiv = document.getElementById('comparisonTable');
            
            const materials = getAvailableMaterials();
            
            let tableHTML = `<table class="material-table">`;
            tableHTML += `<thead><tr><th>材质</th><th>温度范围(°C)</th><th>在${temperature}°C时的许用应力(MPa)</th><th>备注</th></tr></thead>`;
            tableHTML += `<tbody>`;
            
            materials.forEach(material => {
                const range = getMaterialTemperatureRange(material);
                const stress = getAllowableStress(material, temperature);
                
                let note = '';
                if (temperature < range.min) {
                    note = '低于最低温度';
                } else if (temperature > range.max) {
                    note = '高于最高温度';
                } else {
                    note = '正常范围';
                }
                
                tableHTML += `<tr>`;
                tableHTML += `<td>${material}</td>`;
                tableHTML += `<td>${range.min} ~ ${range.max}</td>`;
                tableHTML += `<td>${stress}</td>`;
                tableHTML += `<td>${note}</td>`;
                tableHTML += `</tr>`;
            });
            
            tableHTML += `</tbody></table>`;
            tableDiv.innerHTML = tableHTML;
        }

        // 初始化材质选择器
        function initializeMaterialSelector() {
            const materials = getAvailableMaterials();
            const selector = document.getElementById('materialSelector');

            materials.forEach(material => {
                const div = document.createElement('div');
                div.className = 'material-checkbox';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = `material_${material}`;
                checkbox.value = material;
                checkbox.checked = true; // 默认全选
                checkbox.addEventListener('change', updateChart);

                const label = document.createElement('label');
                label.htmlFor = `material_${material}`;
                label.textContent = material;
                label.style.color = materialColors[material] || '#333';

                div.appendChild(checkbox);
                div.appendChild(label);
                selector.appendChild(div);
            });
        }

        // 初始化图表
        function initializeChart() {
            const ctx = document.getElementById('stressChart').getContext('2d');

            stressChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '材质许用应力-温度曲线图'
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: '温度 (°C)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '许用应力 (MPa)'
                            },
                            beginAtZero: true
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            // 初始加载图表数据
            updateChart();
        }

        // 更新图表
        function updateChart() {
            if (!stressChart) return;

            const tempMin = parseFloat(document.getElementById('tempMin').value) || 0;
            const tempMax = parseFloat(document.getElementById('tempMax').value) || 600;

            // 获取选中的材质
            const selectedMaterials = [];
            const checkboxes = document.querySelectorAll('#materialSelector input[type="checkbox"]:checked');
            checkboxes.forEach(checkbox => {
                selectedMaterials.push(checkbox.value);
            });

            // 生成数据集
            const datasets = [];

            selectedMaterials.forEach(material => {
                const materialData = MATERIAL_ALLOWABLE_STRESS[material];
                if (!materialData) return;

                // 生成温度点数据
                const dataPoints = [];
                const step = (tempMax - tempMin) / 100; // 生成100个点

                for (let temp = tempMin; temp <= tempMax; temp += step) {
                    // 检查温度是否在材质范围内
                    const materialRange = getMaterialTemperatureRange(material);
                    if (materialRange && temp >= materialRange.min && temp <= materialRange.max) {
                        try {
                            // 临时禁用控制台输出以避免大量日志
                            const originalLog = console.log;
                            const originalWarn = console.warn;
                            console.log = function() {};
                            console.warn = function() {};

                            const stress = getAllowableStress(material, temp);
                            dataPoints.push({ x: temp, y: stress });

                            // 恢复控制台输出
                            console.log = originalLog;
                            console.warn = originalWarn;
                        } catch (error) {
                            // 忽略错误，继续下一个点
                        }
                    }
                }

                if (dataPoints.length > 0) {
                    datasets.push({
                        label: material,
                        data: dataPoints,
                        borderColor: materialColors[material] || '#333',
                        backgroundColor: materialColors[material] || '#333',
                        fill: false,
                        tension: 0.1,
                        pointRadius: 1,
                        pointHoverRadius: 5
                    });
                }
            });

            // 更新图表数据
            stressChart.data.datasets = datasets;
            stressChart.options.scales.x.min = tempMin;
            stressChart.options.scales.x.max = tempMax;
            stressChart.update();
        }

        // 全选材质
        function selectAllMaterials() {
            const checkboxes = document.querySelectorAll('#materialSelector input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateChart();
        }

        // 清空选择
        function clearAllMaterials() {
            const checkboxes = document.querySelectorAll('#materialSelector input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateChart();
        }
    </script>
</body>
</html>
