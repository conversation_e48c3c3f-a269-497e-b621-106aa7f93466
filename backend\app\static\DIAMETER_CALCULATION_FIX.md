# 内径计算公式修正说明

## 问题描述

在原始的管道内径计算函数中，气体内径计算公式存在错误，没有正确考虑推荐流速参数，导致计算结果不准确。

## 原始错误公式

### 气体内径计算（修正前）
```javascript
if (config.type === 'gas') {
    const Rg = config.Rg;
    const absP = pressure;
    const tempK = temperature;
    const density = config.density;
    
    // 错误的公式
    const d = Math.sqrt((flow_kg_s * Rg * tempK) / (absP * Math.PI / 4));
    return (d * 1000).toFixed(2);
}
```

**问题分析：**
1. 公式中没有考虑推荐流速 `velocity` 参数
2. 公式的物理意义不明确
3. 与液体计算的逻辑不一致

## 修正后的正确公式

### 统一的内径计算方法
```javascript
// 对于气体和液体都使用相同的连续性方程
function calculatePipeInnerDiameter({ medium, flow_kg_s }) {
    const config = MEDIUM_CONFIG[medium];
    
    if (config.type === 'gas') {
        const density = config.density; // 已在convertUnits中计算
        const velocity = config.velocity; // 推荐流速
        
        // 体积流量 = 质量流量 / 密度
        const Q_v = flow_kg_s / density;
        
        // 连续性方程: d = sqrt((4 * Q_v) / (π * v))
        const d = Math.sqrt((4 * Q_v) / (Math.PI * velocity));
        
        return (d * 1000).toFixed(2);
    } else if (config.type === 'liquid') {
        // 液体计算逻辑保持不变，但统一使用相同公式
        const density = config.density;
        const velocity = config.velocity;
        
        const Q_v = flow_kg_s / density;
        const d = Math.sqrt((4 * Q_v) / (Math.PI * velocity));
        
        return (d * 1000).toFixed(2);
    }
}
```

## 修正要点

### 1. 统一的理论基础
- **连续性方程**：`Q = A × v = (π × d²/4) × v`
- **体积流量**：`Q_v = m / ρ`（质量流量除以密度）
- **内径计算**：`d = sqrt((4 × Q_v) / (π × v))`

### 2. 正确的参数使用
- **密度计算**：气体密度在 `convertUnits` 函数中已正确计算：`ρ = P / (Rg × T)`
- **推荐流速**：使用配置中的 `velocity` 参数
- **体积流量**：通过质量流量和密度计算得出

### 3. 物理意义明确
- 公式基于流体力学基本原理
- 考虑了实际工程中的流速要求
- 气体和液体使用统一的计算逻辑

## 计算过程详解

### 气体内径计算示例
假设：压缩空气，P = 1 MPa，T = 20°C，m = 0.1 kg/s

1. **密度计算**：
   ```
   ρ = P / (Rg × T)
   ρ = 1,000,000 / (287.1 × 293.15)
   ρ = 11.87 kg/m³
   ```

2. **体积流量计算**：
   ```
   Q_v = m / ρ
   Q_v = 0.1 / 11.87
   Q_v = 0.00843 m³/s
   ```

3. **内径计算**：
   ```
   d = sqrt((4 × Q_v) / (π × v))
   d = sqrt((4 × 0.00843) / (π × 50))
   d = sqrt(0.03372 / 157.08)
   d = sqrt(0.0002147)
   d = 0.01465 m = 14.65 mm
   ```

### 液体内径计算示例
假设：水，m = 5 kg/s

1. **密度**：ρ = 1000 kg/m³（常数）

2. **体积流量计算**：
   ```
   Q_v = m / ρ
   Q_v = 5 / 1000
   Q_v = 0.005 m³/s
   ```

3. **内径计算**：
   ```
   d = sqrt((4 × Q_v) / (π × v))
   d = sqrt((4 × 0.005) / (π × 3))
   d = sqrt(0.02 / 9.425)
   d = sqrt(0.002122)
   d = 0.04607 m = 46.07 mm
   ```

## 修正的技术细节

### 1. 函数签名简化
```javascript
// 修正前
function calculatePipeInnerDiameter({ medium, pressure, temperature, flow_kg_s })

// 修正后
function calculatePipeInnerDiameter({ medium, flow_kg_s })
```

**原因**：密度已在 `convertUnits` 函数中计算并存储在 `config.density` 中，不需要重复传递压力和温度参数。

### 2. 调用点更新
```javascript
// 修正前
const calcInnerDiameter = parseFloat(calculatePipeInnerDiameter({ 
    medium, pressure, temperature, flow_kg_s 
}));

// 修正后
const calcInnerDiameter = parseFloat(calculatePipeInnerDiameter({ 
    medium, flow_kg_s 
}));
```

### 3. 增强的日志输出
添加了详细的计算过程日志，便于调试和验证：
```javascript
console.log('****气体内径计算过程****');
console.log(`介质：${medium}`);
console.log(`质量流量：${flow_kg_s} kg/s`);
console.log(`密度：${density.toFixed(3)} kg/m³`);
console.log(`体积流量：${Q_v.toFixed(6)} m³/s`);
console.log(`推荐流速：${velocity} m/s`);
console.log(`计算内径：${(d * 1000).toFixed(2)} mm`);
```

## 验证测试

### 测试用例
创建了完整的测试套件来验证修正后的计算：

1. **单元测试**：`test_diameter_calculation.js`
2. **交互测试页面**：`test_diameter_calculation.html`
3. **对比测试**：修正前后公式结果对比

### 测试覆盖
- ✅ 气体内径计算（压缩空气）
- ✅ 液体内径计算（水、航空煤油）
- ✅ 不同压力和温度条件
- ✅ 不同流量范围
- ✅ 手动计算验证
- ✅ 修正前后对比

## 影响评估

### 正面影响
1. **计算准确性**：内径计算结果更符合工程实际
2. **理论一致性**：气体和液体使用统一的计算原理
3. **工程实用性**：考虑了推荐流速要求
4. **代码简洁性**：简化了函数参数和调用

### 兼容性
- ✅ 不影响现有的用户界面
- ✅ 不改变输入输出格式
- ✅ 保持向后兼容
- ✅ 增强了计算精度

## 使用建议

1. **验证计算结果**：使用测试页面验证特定工况下的计算结果
2. **查看计算日志**：通过浏览器控制台查看详细的计算过程
3. **对比分析**：对于关键项目，可以手动验证计算结果
4. **参数合理性**：确保输入的流量和工况参数在合理范围内

## 总结

这次修正解决了气体内径计算中的根本性错误，使计算结果更加准确和可靠。修正后的公式基于流体力学基本原理，考虑了实际工程需求，为管道设计提供了更可靠的技术支持。
