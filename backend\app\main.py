# 标准库导入
import os
import sys
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from contextlib import asynccontextmanager

# 第三方库导入
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

# 本地应用导入
from .core import settings, logger, templates, static_files, lifespan, setup_app_resources, ensure_directories, register_filters
from .database import get_db, engine, Base
from .models import file as file_models, RawFile, ParquetFile, raw_parquet_association, ProcessStatus
from .services import FileService, TaskProcessor
from .api.endpoints.router import api_router
from .services.file_service import FileService
from .services.parquet_service import ParquetService


# 创建数据库表
Base.metadata.create_all(bind=engine)

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent
logger.info(f"项目根目录: {BASE_DIR}")

# 定义数据目录
DATA_DIR = BASE_DIR / "data"
RAW_DIR = DATA_DIR / "raw"
PROCESSED_DIR = DATA_DIR / "processed"

# 确保目录存在
RAW_DIR.mkdir(parents=True, exist_ok=True)
PROCESSED_DIR.mkdir(parents=True, exist_ok=True)

logger.info(f"原始数据目录: {RAW_DIR}")
logger.info(f"处理后数据目录: {PROCESSED_DIR}")

# 全局任务处理器
task_processor = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    处理应用的启动和关闭事件
    """
    # 启动时的操作
    global task_processor
    try:
        logger.info("正在启动应用...")

        # 初始化任务处理器
        task_processor = TaskProcessor(settings.PROCESSED_DIR)
        # 在后台启动任务处理器
        background_task = asyncio.create_task(task_processor.start())

        logger.info("应用启动完成")
        yield

        # 关闭时的操作
        logger.info("正在关闭应用...")
        if task_processor:
            await task_processor.stop()
            try:
                await background_task
            except Exception as e:
                logger.error(f"关闭后台任务时出错: {str(e)}")

        logger.info("应用已关闭")

    except Exception as e:
        logger.error(f"应用生命周期管理出错: {str(e)}")
        raise

# 创建 FastAPI 应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="数据文件处理与可视化平台API",
    docs_url="/docs",  # 修改文档路由
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    # 指定本地 Swagger UI 资源路径
    swagger_js_url="/static/swagger-ui/swagger-ui-bundle.min.js",
    swagger_css_url="/static/swagger-ui/swagger-ui.min.css",
    # 可选：指定本地 favicon
    # swagger_favicon_url="/static/swagger-ui/favicon.png",
    lifespan=lifespan
)

# 初始化服务
file_service = FileService(settings.RAW_DIR, settings.PROCESSED_DIR)

# 设置应用资源
setup_app_resources(app)

# 注册API路由
app.include_router(api_router)

# 初始化模板
templates = Jinja2Templates(directory=settings.TEMPLATES_DIR)

logger.info(f"应用创建完成，API文档: http://{settings.HOST}:{settings.PORT}/docs")

def format_size(size_in_bytes: float) -> str:
    """格式化文件大小"""
    try:
        if not isinstance(size_in_bytes, (int, float)):
            return "0 B"

        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_in_bytes < 1024:
                return f"{size_in_bytes:.2f} {unit}"
            size_in_bytes /= 1024
        return f"{size_in_bytes:.2f} TB"
    except Exception as e:
        logger.error(f"格式化文件大小失败: {str(e)}")
        return "未知大小"


def format_number(value):
    """格式化数字，添加千位分隔符"""
    try:
        return f"{int(value):,}"
    except (ValueError, TypeError):
        return "0"


# 确保在使用模板之前注册过滤器
templates.env.filters["format_size"] = format_size
templates.env.filters["format_number"] = format_number


@app.post("/api/v1/files/{file_id}/process")
async def process_file(file_id: int):
    """手动处理指定文件"""
    success = await TaskProcessor.manual_process_file(file_id)
    if success:
        return {"status": "success", "message": "文件处理完成"}
    else:
        return {"status": "error", "message": "文件处理失败"}


@app.get("/api/v1/files/pending")
async def get_pending_files(db: Session = Depends(get_db)):
    """获取待处理文件列表"""
    try:
        pending_files = db.query(file_models.RawFile).filter(
            file_models.RawFile.status == "uploaded",
            ~file_models.RawFile.time_series_segments.any()
        ).all()

        return {
            "status": "success",
            "count": len(pending_files),
            "files": [
                {
                    "id": f.id,
                    "filename": f.filename,
                    "upload_time": f.upload_time,
                    "status": f.status
                }
                for f in pending_files
            ]
        }
    except Exception as e:
        logger.error(f"获取待处理文件列表失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }


@app.get("/status", response_class=HTMLResponse)
async def status(request: Request):
    """系统状态页面"""
    logger.debug("开始处理状态页面请求")
    try:
        # 获取待处理文件列表
        pending_files = await RawFile.get_pending_files()
        logger.debug(f"获取到 {len(pending_files)} 个待处理文件")

        # 获取错误文件列表
        error_files = await RawFile.get_error_files()
        logger.debug(f"获取到 {len(error_files)} 个错误文件")

        # 获取处理中的文件列表
        processing_files = await RawFile.get_processing_files()
        logger.debug(f"获取到 {len(processing_files)} 个处理中的文件")

        # 获取已完成文件列表
        completed_files = await RawFile.get_completed_files()
        logger.debug(f"获取到 {len(completed_files)} 个已完成文件")

        # 获取系统统计信息
        stats = await RawFile.get_system_stats()
        logger.debug(f"系统统计信息: {stats}")

        # 获取最近处理的文件
        recent_files = await RawFile.get_recent_files(limit=5)
        logger.debug(f"获取到 {len(recent_files)} 个最近处理的文件")

        # 获取处理队列状态
        queue_status = await RawFile.get_queue_status()
        logger.debug(f"处理队列状态: {queue_status}")

        # 获取系统资源使用情况
        system_usage = await RawFile.get_system_usage()
        logger.debug(f"系统资源使用情况: {system_usage}")

        logger.debug("准备渲染状态页面模板")
        return templates.TemplateResponse(
            "status.html",
            {
                "request": request,
                "pending_files": pending_files,
                "error_files": error_files,
                "processing_files": processing_files,
                "completed_files": completed_files,
                "stats": stats,
                "recent_files": recent_files,
                "queue_status": queue_status,
                "system_usage": system_usage
            }
        )
    except Exception as e:
        logger.error(f"处理状态页面请求时发生错误: {str(e)}", exc_info=True)
        raise


@app.get("/api/v1/files/hashes")
async def get_file_hashes(db: Session = Depends(get_db)):
    """获取系统中所有文件的哈希值"""
    try:
        # 获取所有文件哈希
        hashes = db.query(RawFile.file_hash).filter(
            RawFile.file_hash.isnot(None)
        ).all()

        # 将结果转换为列表
        hash_list = [h[0] for h in hashes]

        return {
            "status": "success",
            "count": len(hash_list),
            "hashes": hash_list
        }
    except Exception as e:
        logger.error(f"获取文件哈希失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }


@app.get("/api/v1/test-db")
async def test_database():
    """测试数据库连接"""
    from .database import test_db_connection

    success = test_db_connection()

    if success:
        # 尝试插入一条测试记录
        try:
            db = next(get_db())
            test_record = RawFile(
                filename="test.csv",
                file_path="/tmp/test.csv",
                file_size=100.0,
                file_hash="test_hash",
                mime_type="text/csv",
                status=ProcessStatus.PENDING
            )
            db.add(test_record)
            db.commit()
            record_id = test_record.id
            db.delete(test_record)
            db.commit()

            return {
                "status": "success",
                "message": "数据库连接和操作测试成功",
                "test_record_id": record_id
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"数据库操作测试失败: {str(e)}"
            }
    else:
        return {
            "status": "error",
            "message": "数据库连接测试失败"
        }


def get_file_segments(db: Session, raw_file_id: int) -> list:
    """获取文件的时间序列段信息"""
    try:
        # 获取原始文件
        raw_file = db.query(RawFile).filter(RawFile.id == raw_file_id).first()
        if not raw_file:
            return []

        # 获取关联的Parquet文件
        segments = db.query(ParquetFile).filter(
            ParquetFile.raw_file_id == raw_file_id
        ).all()

        return segments
    except Exception as e:
        logger.error(f"获取文件段信息失败: {str(e)}")
        return []


@app.post("/api/v1/files/{file_id}/retry")
async def retry_file_processing(file_id: int, db: Session = Depends(get_db)):
    """将错误文件重新加入处理队列"""
    try:
        # 获取文件记录
        file = db.query(RawFile).filter(RawFile.id == file_id).first()
        if not file:
            return {"status": "error", "message": "文件不存在"}

        # 检查文件状态是否为错误
        if file.status != ProcessStatus.ERROR:
            return {"status": "error", "message": "只有错误状态的文件可以重试"}

        # 更新文件状态为待处理
        file.status = ProcessStatus.PENDING
        file.error_message = None
        db.commit()

        return {"status": "success", "message": "文件已重新加入处理队列"}
    except Exception as e:
        logger.error(f"重试文件处理失败: {str(e)}")
        return {"status": "error", "message": str(e)}
