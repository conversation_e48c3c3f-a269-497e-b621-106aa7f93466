<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 2rem;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .welcome-container {
            text-align: center;
            padding: 2rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        h1 {
            color: #333;
            margin-bottom: 1rem;
        }

        .category {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .category h2 {
            color: #333;
            margin-top: 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #eee;
        }

        .link-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .link-list li {
            margin: 0.5rem 0;
        }

        .link-list a {
            color: #0066cc;
            text-decoration: none;
            display: inline-block;
            padding: 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .link-list a:hover {
            background-color: #f0f0f0;
        }

        .description {
            color: #666;
            font-size: 0.9rem;
            margin-left: 0.5rem;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="welcome-container">
            <h1>欢迎</h1>
            <p>欢迎使用数据文件处理与可视化平台</p>
        </div>

        <div class="category">
            <h2>API 文档</h2>
            <ul class="link-list">
                <li>
                    <a href="/docs">Swagger UI 文档</a>
                    <span class="description">- 交互式 API 文档</span>
                </li>
                <li>
                    <a href="/redoc">ReDoc 文档</a>
                    <span class="description">- 另一种 API 文档格式</span>
                </li>
            </ul>
        </div>

        <div class="category">
            <h2>计算器工具</h2>
            <ul class="link-list">
                <li>
                    <a href="/pipe-calculator">管道计算器</a>
                    <span class="description">- 管道相关计算工具</span>
                </li>
                <li>
                    <a href="/combustion-calculator">燃烧计算器</a>
                    <span class="description">- 燃烧相关计算工具</span>
                </li>
            </ul>
        </div>

        <div class="category">
            <h2>测试工具</h2>
            <ul class="link-list">
                <li>
                    <a href="/test-material-stress">材质许用应力测试</a>
                    <span class="description">- 材质许用应力功能测试与曲线图</span>
                </li>
                <li>
                    <a href="/test-diameter-calculation">内径计算验证</a>
                    <span class="description">- 内径计算公式验证测试</span>
                </li>
            </ul>
        </div>

        <div class="category">
            <h2>系统状态</h2>
            <ul class="link-list">
                <li>
                    <a href="/status">系统状态</a>
                    <span class="description">- 查看系统运行状态和统计信息</span>
                </li>
            </ul>
        </div>

        <div class="category">
            <h2>文件处理</h2>
            <ul class="link-list">
                <li>
                    <a href="/api/v1/files/pending">待处理文件</a>
                    <span class="description">- 查看待处理的文件列表</span>
                </li>
                <li>
                    <a href="/api/v1/files/hashes">文件哈希</a>
                    <span class="description">- 查看所有文件的哈希值</span>
                </li>
            </ul>
        </div>
    </div>
</body>

</html>