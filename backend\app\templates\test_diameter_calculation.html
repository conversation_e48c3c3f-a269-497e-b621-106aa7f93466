<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内径计算公式验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        label {
            min-width: 120px;
            font-weight: bold;
        }
        select, input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            flex: 1;
            max-width: 200px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .formula-section {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .formula {
            font-family: 'Times New Roman', serif;
            font-size: 16px;
            text-align: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        .test-cases {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-case {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .test-case h4 {
            margin-top: 0;
            color: #495057;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .comparison-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .error-highlight {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .success-highlight {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>内径计算公式验证</h1>
        
        <div class="formula-section">
            <h2>计算公式</h2>
            <div class="formula">
                D = √(4 × ρ × Q / (π × v))
            </div>
            <p><strong>其中：</strong></p>
            <ul>
                <li>D = 管道内径 (m)</li>
                <li>ρ = 介质密度 (kg/m³)</li>
                <li>Q = 质量流量 (kg/s)</li>
                <li>v = 流速 (m/s)</li>
                <li>π = 圆周率 (3.14159...)</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>1. 单个验证测试</h2>
            <div class="input-group">
                <label for="medium">介质:</label>
                <select id="medium">
                    <option value="压缩空气">压缩空气</option>
                    <option value="水">水</option>
                    <option value="航空煤油">航空煤油</option>
                </select>
            </div>
            <div class="input-group">
                <label for="flowRate">流量 (kg/s):</label>
                <input type="number" id="flowRate" value="10" step="0.1" min="0.1">
            </div>
            <div class="input-group">
                <label for="velocity">流速 (m/s):</label>
                <input type="number" id="velocity" value="3" step="0.1" min="0.1">
            </div>
            <button onclick="runSingleTest()">验证计算</button>
            <div id="singleResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>2. 批量验证测试</h2>
            <button onclick="runBatchTests()">运行批量测试</button>
            <button onclick="runAccuracyTests()">精度验证测试</button>
            <button onclick="runBoundaryTests()">边界条件测试</button>
            <div id="batchResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>3. 预设测试用例</h2>
            <div class="test-cases">
                <div class="test-case">
                    <h4>水 - 低流量</h4>
                    <p>流量: 1 kg/s, 流速: 2 m/s</p>
                    <button onclick="runPresetTest('水', 1, 2)">测试</button>
                </div>
                <div class="test-case">
                    <h4>水 - 中等流量</h4>
                    <p>流量: 10 kg/s, 流速: 3 m/s</p>
                    <button onclick="runPresetTest('水', 10, 3)">测试</button>
                </div>
                <div class="test-case">
                    <h4>压缩空气 - 高流速</h4>
                    <p>流量: 5 kg/s, 流速: 50 m/s</p>
                    <button onclick="runPresetTest('压缩空气', 5, 50)">测试</button>
                </div>
                <div class="test-case">
                    <h4>航空煤油 - 标准条件</h4>
                    <p>流量: 8 kg/s, 流速: 3 m/s</p>
                    <button onclick="runPresetTest('航空煤油', 8, 3)">测试</button>
                </div>
            </div>
            <div id="presetResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>4. 详细对比验证</h2>
            <div class="input-group">
                <label for="detailMedium">介质:</label>
                <select id="detailMedium">
                    <option value="水">水</option>
                    <option value="压缩空气">压缩空气</option>
                    <option value="航空煤油">航空煤油</option>
                </select>
            </div>
            <div class="input-group">
                <label for="detailFlow">流量 (kg/s):</label>
                <input type="number" id="detailFlow" value="5" step="0.1" min="0.1">
            </div>
            <button onclick="runDetailedComparison()">详细对比</button>
            <div id="detailedResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入主计算器脚本 -->
    <script src="/static/js/pipe_calculator.js"></script>
    <!-- 引入测试脚本 -->
    <script src="/static/js/test_diameter_calculation.js"></script>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('内径计算验证页面已加载');
            console.log('可用介质:', Object.keys(MEDIUM_CONFIG));
        });

        // 单个验证测试
        function runSingleTest() {
            const medium = document.getElementById('medium').value;
            const flowRate = parseFloat(document.getElementById('flowRate').value);
            const velocity = parseFloat(document.getElementById('velocity').value);
            const resultDiv = document.getElementById('singleResult');
            
            try {
                console.clear();
                
                // 使用系统函数计算
                const systemResult = parseFloat(calculatePipeInnerDiameter({ medium, flow_kg_s: flowRate }));
                
                // 手动计算验证
                const config = MEDIUM_CONFIG[medium];
                const density = config.density || 1;
                const d_m = Math.sqrt(4 * density * flowRate / (Math.PI * velocity));
                const d_mm = d_m * 1000;
                
                let result = `验证结果:\n`;
                result += `介质: ${medium}\n`;
                result += `密度: ${density} kg/m³\n`;
                result += `流量: ${flowRate} kg/s\n`;
                result += `流速: ${velocity} m/s\n\n`;
                result += `系统计算结果: ${systemResult} mm\n`;
                result += `手动计算结果: ${d_mm.toFixed(2)} mm\n`;
                result += `计算误差: ${Math.abs(systemResult - d_mm).toFixed(4)} mm\n`;
                
                if (Math.abs(systemResult - d_mm) < 0.01) {
                    result += `✅ 验证通过 - 误差在可接受范围内\n`;
                    resultDiv.className = 'result success-highlight';
                } else {
                    result += `❌ 验证失败 - 误差过大\n`;
                    resultDiv.className = 'result error-highlight';
                }
                
                resultDiv.textContent = result;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error-highlight';
                resultDiv.style.display = 'block';
            }
        }

        // 批量验证测试
        function runBatchTests() {
            const resultDiv = document.getElementById('batchResult');
            console.clear();

            // 捕获控制台输出
            const originalLog = console.log;
            const originalError = console.error;
            let output = '';

            console.log = function(...args) {
                output += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };

            console.error = function(...args) {
                output += 'ERROR: ' + args.join(' ') + '\n';
                originalError.apply(console, args);
            };

            try {
                runDiameterCalculationTests();

                // 恢复原始console函数
                console.log = originalLog;
                console.error = originalError;

                resultDiv.textContent = output;
                resultDiv.style.display = 'block';

            } catch (error) {
                console.log = originalLog;
                console.error = originalError;

                resultDiv.textContent = `测试执行错误: ${error.message}\n${output}`;
                resultDiv.style.display = 'block';
            }
        }

        // 精度验证测试
        function runAccuracyTests() {
            const resultDiv = document.getElementById('batchResult');
            console.clear();

            let output = '';
            const originalLog = console.log;
            console.log = function(...args) {
                output += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };

            compareOldAndNewFormulas();

            console.log = originalLog;
            resultDiv.textContent = output;
            resultDiv.style.display = 'block';
        }

        // 边界条件测试
        function runBoundaryTests() {
            const resultDiv = document.getElementById('batchResult');
            let result = '边界条件测试结果:\n';
            result += '==================\n\n';

            const testCases = [
                { medium: '水', flow: 0.1, velocity: 0.5, desc: '极低流量' },
                { medium: '水', flow: 100, velocity: 5, desc: '极高流量' },
                { medium: '压缩空气', flow: 0.01, velocity: 10, desc: '气体低流量' },
                { medium: '压缩空气', flow: 50, velocity: 100, desc: '气体高流速' }
            ];

            testCases.forEach((testCase, index) => {
                try {
                    const systemResult = parseFloat(calculatePipeInnerDiameter({
                        medium: testCase.medium,
                        flow_kg_s: testCase.flow
                    }));

                    const config = MEDIUM_CONFIG[testCase.medium];
                    const density = config.density || 1;
                    const d_m = Math.sqrt(4 * density * testCase.flow / (Math.PI * testCase.velocity));
                    const d_mm = d_m * 1000;

                    result += `测试 ${index + 1}: ${testCase.desc}\n`;
                    result += `  介质: ${testCase.medium}\n`;
                    result += `  流量: ${testCase.flow} kg/s\n`;
                    result += `  系统结果: ${systemResult} mm\n`;
                    result += `  手动计算: ${d_mm.toFixed(2)} mm\n`;
                    result += `  误差: ${Math.abs(systemResult - d_mm).toFixed(4)} mm\n\n`;

                } catch (error) {
                    result += `测试 ${index + 1}: ${testCase.desc} - 错误: ${error.message}\n\n`;
                }
            });

            resultDiv.textContent = result;
            resultDiv.style.display = 'block';
        }

        // 预设测试用例
        function runPresetTest(medium, flow, velocity) {
            const resultDiv = document.getElementById('presetResult');

            try {
                const systemResult = parseFloat(calculatePipeInnerDiameter({
                    medium: medium,
                    flow_kg_s: flow
                }));

                const config = MEDIUM_CONFIG[medium];
                const density = config.density || 1;
                const d_m = Math.sqrt(4 * density * flow / (Math.PI * velocity));
                const d_mm = d_m * 1000;

                let result = `预设测试结果:\n`;
                result += `介质: ${medium}\n`;
                result += `流量: ${flow} kg/s\n`;
                result += `流速: ${velocity} m/s\n`;
                result += `密度: ${density} kg/m³\n\n`;
                result += `系统计算: ${systemResult} mm\n`;
                result += `手动计算: ${d_mm.toFixed(2)} mm\n`;
                result += `计算误差: ${Math.abs(systemResult - d_mm).toFixed(4)} mm\n`;

                if (Math.abs(systemResult - d_mm) < 0.01) {
                    result += `✅ 验证通过\n`;
                    resultDiv.className = 'result success-highlight';
                } else {
                    result += `❌ 验证失败\n`;
                    resultDiv.className = 'result error-highlight';
                }

                resultDiv.textContent = result;
                resultDiv.style.display = 'block';

            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error-highlight';
                resultDiv.style.display = 'block';
            }
        }

        // 详细对比验证
        function runDetailedComparison() {
            const medium = document.getElementById('detailMedium').value;
            const flow = parseFloat(document.getElementById('detailFlow').value);
            const resultDiv = document.getElementById('detailedResult');

            const velocities = [1, 2, 3, 4, 5, 10, 20, 50];
            let result = `详细对比验证 - ${medium}\n`;
            result += `流量: ${flow} kg/s\n`;
            result += `==================\n\n`;

            const config = MEDIUM_CONFIG[medium];
            const density = config.density || 1;

            result += `介质密度: ${density} kg/m³\n`;
            result += `推荐流速: ${config.velocity} m/s\n\n`;

            result += `流速(m/s) | 系统计算(mm) | 手动计算(mm) | 误差(mm)\n`;
            result += `---------|-------------|-------------|----------\n`;

            velocities.forEach(velocity => {
                try {
                    // 注意：系统计算使用推荐流速，这里只是对比不同流速下的理论值
                    const systemResult = parseFloat(calculatePipeInnerDiameter({
                        medium: medium,
                        flow_kg_s: flow
                    }));

                    const d_m = Math.sqrt(4 * density * flow / (Math.PI * velocity));
                    const d_mm = d_m * 1000;
                    const error = Math.abs(systemResult - d_mm);

                    result += `${velocity.toString().padStart(8)} | `;
                    result += `${systemResult.toString().padStart(11)} | `;
                    result += `${d_mm.toFixed(2).padStart(11)} | `;
                    result += `${error.toFixed(4).padStart(8)}\n`;

                } catch (error) {
                    result += `${velocity.toString().padStart(8)} | 错误: ${error.message}\n`;
                }
            });

            result += `\n注意: 系统计算使用推荐流速 ${config.velocity} m/s\n`;
            result += `手动计算显示不同流速下的理论内径值\n`;

            resultDiv.textContent = result;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
