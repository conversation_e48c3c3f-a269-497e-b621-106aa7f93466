# 管道计算器功能说明

## 概述

管道计算选型工具是一个专业的工程计算软件，用于压力管道的设计计算和选型。本工具集成了材质许用应力数据库，能够根据材质和温度自动计算许用应力，提高计算精度和设计效率。

## 主要功能模块

### 1. 基础设置区域

#### 项目信息
- **项目名称**：自动生成带日期的项目名称
- **公称压力标准**：支持PN系列和CLASS系列
- **管道系列**：A系列和B系列管道外径标准

#### 管道位号设置
- **管道材质选择**：支持6种常用管道材质
  - 20G钢（碳钢，20-500°C）
  - 16MnR钢（低合金钢，20-500°C）
  - 15CrMoR钢（珠光体耐热钢，20-600°C）
  - 12Cr1MoVR钢（珠光体耐热钢，20-600°C）
  - 304不锈钢（奥氏体不锈钢，20-700°C）
  - 316L不锈钢（低碳奥氏体不锈钢，20-700°C）

- **介质选择**：压缩空气、水、航空煤油
- **推荐流速**：根据介质自动设置推荐流速

#### 材质信息显示
- **温度范围**：显示所选材质的适用温度范围
- **查询温度**：可输入任意温度查询许用应力
- **许用应力显示**：实时显示当前温度下的许用应力值
- **状态指示**：显示查询温度是否在材质适用范围内

#### 单位设置
- **压力单位**：MPa、bar、kPa
- **温度单位**：°C、K
- **流量单位**：kg/h、kg/s、m³/h、m³/s

### 2. 材质对比工具

#### 材质对比表
- 输入对比温度，生成所有材质在该温度下的许用应力对比表
- 显示各材质的温度范围和适用状态
- 帮助工程师选择最适合的材质

#### 温度-应力曲线数据
- 选择特定材质，生成温度-应力关系数据
- 显示材质在整个温度范围内的应力变化
- 提供数据点用于绘制曲线图

### 3. 数据输入区域

#### 数据表格
- **位号**：管道编号（可编辑）
- **流体介质**：自动填充选择的介质
- **管道材质**：自动填充选择的材质
- **压力**：设计压力（可编辑）
- **温度**：设计温度（可编辑）
- **流量**：设计流量（可编辑）
- **管道规格**：自动计算的管道规格
- **保温层厚**：保温层厚度（待实现）
- **流速标准**：计算得出的流速

#### 操作功能
- **添加行**：添加新的计算行
- **计算**：执行所有行的计算
- **导入数据**：从CSV文件导入数据
- **导出报告**：导出计算结果为CSV文件

### 4. 可视化图表区域

提供压力、温度、流量的可视化图表展示（使用Bokeh库）。

### 5. 设计说明区域

#### 管道设计规范说明
- 压力等级标准
- 材质选择原则
- 许用应力功能说明
- 壁厚计算方法
- 流速限制要求

#### 材质许用应力功能
- 智能查表机制
- 线性插值算法
- 边界条件处理
- 材质对比功能
- 各材质温度范围

#### 计算依据
- 相关设计标准
- 计算公式说明
- 安全系数考虑

## 核心算法

### 许用应力查询算法

```javascript
function getAllowableStress(material, temperature) {
    // 1. 获取材质数据
    const materialData = MATERIAL_ALLOWABLE_STRESS[material];
    
    // 2. 边界处理
    if (temperature <= minTemp) return minStress;
    if (temperature >= maxTemp) return maxStress;
    
    // 3. 线性插值
    for (温度区间) {
        stress = s1 + (s2 - s1) * (t - t1) / (t2 - t1);
    }
    
    return stress;
}
```

### 管道规格计算流程

1. **内径计算**：根据流量、介质密度、推荐流速计算所需内径
2. **许用应力查询**：根据材质和温度查询许用应力
3. **壁厚计算**：使用标准公式计算所需壁厚
4. **规格选择**：选择满足要求的标准管道规格
5. **流速验证**：验证实际流速是否符合要求

## 技术特点

### 1. 精确计算
- 基于标准许用应力数据
- 线性插值确保任意温度点精度
- 考虑材质温度特性

### 2. 智能化
- 自动材质适用性检查
- 智能边界条件处理
- 实时计算和显示

### 3. 用户友好
- 直观的界面设计
- 实时反馈和提示
- 详细的计算过程日志

### 4. 专业性
- 符合国家标准
- 工程实用性强
- 计算结果可靠

## 使用流程

1. **设置基础参数**：选择材质、介质、单位等
2. **查看材质信息**：确认材质温度范围和许用应力
3. **输入设计参数**：在表格中输入压力、温度、流量
4. **执行计算**：点击计算按钮获得结果
5. **分析结果**：查看管道规格和流速
6. **材质对比**：如需要，使用材质对比工具
7. **导出报告**：保存计算结果

## 注意事项

1. **温度范围**：确保设计温度在材质适用范围内
2. **压力单位**：注意压力单位的正确选择
3. **流速限制**：关注计算得出的流速是否合理
4. **安全系数**：许用应力已包含安全系数
5. **标准依据**：计算结果基于相关国家标准

## 技术支持

如有技术问题或功能建议，请参考：
- 设计说明区域的详细文档
- 控制台日志输出
- 材质许用应力功能说明文档
