<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内径计算公式验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        select, input {
            padding: 8px;
            margin-left: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 150px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .formula-display {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .comparison-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>内径计算公式验证测试</h1>
        
        <div class="formula-display">
            <h3>修正说明</h3>
            <p><strong>修正前的错误公式：</strong></p>
            <code>d = sqrt((m * Rg * T) / (P * π/4))</code>
            <p><strong>修正后的正确公式：</strong></p>
            <code>d = sqrt((4 * Q_v) / (π * v))</code>
            <p>其中：Q_v = m / ρ（体积流量），v = 推荐流速</p>
        </div>

        <div class="test-section">
            <h2>1. 单个计算测试</h2>
            <div class="input-group">
                <label for="testMedium">介质:</label>
                <select id="testMedium">
                    <option value="压缩空气">压缩空气</option>
                    <option value="水">水</option>
                    <option value="航空煤油">航空煤油</option>
                </select>
            </div>
            <div class="input-group">
                <label for="testPressure">压力(MPa):</label>
                <input type="number" id="testPressure" value="1.0" step="0.1" min="0.1">
            </div>
            <div class="input-group">
                <label for="testTemperature">温度(°C):</label>
                <input type="number" id="testTemperature" value="20" step="1">
            </div>
            <div class="input-group">
                <label for="testFlow">质量流量(kg/s):</label>
                <input type="number" id="testFlow" value="0.1" step="0.01" min="0.01">
            </div>
            <button onclick="runSingleTest()">计算内径</button>
            <div id="singleTestResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>2. 批量验证测试</h2>
            <button onclick="runAllTests()">运行所有测试用例</button>
            <button onclick="compareFormulas()">对比修正前后公式</button>
            <div id="batchTestResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>3. 计算过程详解</h2>
            <p>选择一个测试用例，查看详细的计算过程：</p>
            <button onclick="showDetailedCalculation()">显示详细计算过程</button>
            <div id="detailedResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入主计算器脚本 -->
    <script src="js/pipe_calculator.js"></script>
    <!-- 引入测试脚本 -->
    <script src="js/test_diameter_calculation.js"></script>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('内径计算验证页面已加载');
        });

        // 单个测试功能
        function runSingleTest() {
            const medium = document.getElementById('testMedium').value;
            const pressure = parseFloat(document.getElementById('testPressure').value);
            const temperature = parseFloat(document.getElementById('testTemperature').value);
            const flow = parseFloat(document.getElementById('testFlow').value);
            const resultDiv = document.getElementById('singleTestResult');
            
            try {
                console.clear();
                
                // 1. 单位换算
                const std = convertUnits({
                    pressure: pressure,
                    pressureUnit: 'MPa',
                    temperature: temperature,
                    temperatureUnit: 'C',
                    flow: flow,
                    flowUnit: 'kg/s',
                    medium: medium
                });
                
                // 2. 计算内径
                const diameter = parseFloat(calculatePipeInnerDiameter({
                    medium: medium,
                    flow_kg_s: std.flow
                }));
                
                // 3. 手动验证
                const manualResult = manualCalculation({
                    name: '单个测试',
                    medium: medium,
                    pressure: std.pressure,
                    temperature: std.temperature,
                    flow_kg_s: std.flow,
                    expected_diameter_range: [0, 1000]
                });
                
                let result = `计算结果:\n`;
                result += `介质: ${medium}\n`;
                result += `压力: ${pressure} MPa\n`;
                result += `温度: ${temperature}°C\n`;
                result += `质量流量: ${flow} kg/s\n\n`;
                result += `系统计算内径: ${diameter} mm\n`;
                result += `手动验证内径: ${manualResult.diameter_mm.toFixed(2)} mm\n`;
                result += `计算误差: ${Math.abs(diameter - manualResult.diameter_mm).toFixed(4)} mm\n\n`;
                result += `密度: ${manualResult.density.toFixed(3)} kg/m³\n`;
                result += `体积流量: ${manualResult.volumeFlow.toFixed(6)} m³/s\n`;
                
                resultDiv.textContent = result;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        // 运行所有测试
        function runAllTests() {
            const resultDiv = document.getElementById('batchTestResult');
            console.clear();
            
            // 捕获控制台输出
            const originalLog = console.log;
            const originalError = console.error;
            let output = '';
            
            console.log = function(...args) {
                output += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };
            
            console.error = function(...args) {
                output += 'ERROR: ' + args.join(' ') + '\n';
                originalError.apply(console, args);
            };
            
            try {
                runDiameterCalculationTests();
                
                // 恢复原始console函数
                console.log = originalLog;
                console.error = originalError;
                
                resultDiv.textContent = output;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                console.log = originalLog;
                console.error = originalError;
                
                resultDiv.textContent = `测试执行错误: ${error.message}\n${output}`;
                resultDiv.style.display = 'block';
            }
        }

        // 对比公式
        function compareFormulas() {
            const resultDiv = document.getElementById('batchTestResult');
            console.clear();
            
            let output = '';
            const originalLog = console.log;
            console.log = function(...args) {
                output += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };
            
            compareOldAndNewFormulas();
            
            console.log = originalLog;
            resultDiv.textContent = output;
            resultDiv.style.display = 'block';
        }

        // 显示详细计算过程
        function showDetailedCalculation() {
            const resultDiv = document.getElementById('detailedResult');
            
            const testCase = {
                medium: '压缩空气',
                pressure: 1.5, // MPa
                temperature: 50, // °C
                flow_kg_s: 0.2
            };
            
            let result = `详细计算过程演示\n`;
            result += `==================\n\n`;
            result += `输入参数:\n`;
            result += `介质: ${testCase.medium}\n`;
            result += `压力: ${testCase.pressure} MPa\n`;
            result += `温度: ${testCase.temperature}°C\n`;
            result += `质量流量: ${testCase.flow_kg_s} kg/s\n\n`;
            
            try {
                // 获取介质配置
                const config = MEDIUM_CONFIG[testCase.medium];
                result += `介质配置:\n`;
                result += `类型: ${config.type}\n`;
                if (config.type === 'gas') {
                    result += `气体常数 Rg: ${config.Rg} J/(kg·K)\n`;
                } else {
                    result += `密度: ${config.density} kg/m³\n`;
                }
                result += `推荐流速: ${config.velocity} m/s\n\n`;
                
                // 单位换算
                const std = convertUnits({
                    pressure: testCase.pressure,
                    pressureUnit: 'MPa',
                    temperature: testCase.temperature,
                    temperatureUnit: 'C',
                    flow: testCase.flow_kg_s,
                    flowUnit: 'kg/s',
                    medium: testCase.medium
                });
                
                result += `单位换算后:\n`;
                result += `压力: ${std.pressure} Pa\n`;
                result += `温度: ${std.temperature} K\n`;
                result += `质量流量: ${std.flow} kg/s\n\n`;
                
                // 密度计算
                let density;
                if (config.type === 'gas') {
                    density = std.pressure / (config.Rg * std.temperature);
                    result += `密度计算 (气体):\n`;
                    result += `ρ = P / (Rg × T)\n`;
                    result += `ρ = ${std.pressure} / (${config.Rg} × ${std.temperature})\n`;
                    result += `ρ = ${density.toFixed(3)} kg/m³\n\n`;
                } else {
                    density = config.density;
                    result += `密度 (液体): ${density} kg/m³\n\n`;
                }
                
                // 体积流量计算
                const Q_v = std.flow / density;
                result += `体积流量计算:\n`;
                result += `Q_v = m / ρ\n`;
                result += `Q_v = ${std.flow} / ${density.toFixed(3)}\n`;
                result += `Q_v = ${Q_v.toFixed(6)} m³/s\n\n`;
                
                // 内径计算
                const d_m = Math.sqrt((4 * Q_v) / (Math.PI * config.velocity));
                const d_mm = d_m * 1000;
                
                result += `内径计算:\n`;
                result += `d = sqrt((4 × Q_v) / (π × v))\n`;
                result += `d = sqrt((4 × ${Q_v.toFixed(6)}) / (π × ${config.velocity}))\n`;
                result += `d = sqrt(${(4 * Q_v).toFixed(6)} / ${(Math.PI * config.velocity).toFixed(3)})\n`;
                result += `d = sqrt(${((4 * Q_v) / (Math.PI * config.velocity)).toFixed(6)})\n`;
                result += `d = ${d_m.toFixed(6)} m\n`;
                result += `d = ${d_mm.toFixed(2)} mm\n\n`;
                
                // 系统计算验证
                const systemResult = parseFloat(calculatePipeInnerDiameter({
                    medium: testCase.medium,
                    flow_kg_s: std.flow
                }));
                
                result += `系统计算结果: ${systemResult} mm\n`;
                result += `手动计算结果: ${d_mm.toFixed(2)} mm\n`;
                result += `计算误差: ${Math.abs(systemResult - d_mm).toFixed(4)} mm\n`;
                
            } catch (error) {
                result += `计算错误: ${error.message}\n`;
            }
            
            resultDiv.textContent = result;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
