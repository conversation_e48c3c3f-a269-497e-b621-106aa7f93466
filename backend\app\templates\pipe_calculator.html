<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管道计算选型工具</title>
    <link href="/static/bootstrap-4.6.0-dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/bokeh/bokeh.min.css" rel="stylesheet">
    <link href="/static/bokeh/bokeh-widgets.min.css" rel="stylesheet">
    <link href="/static/bokeh/bokeh-tables.min.css" rel="stylesheet">
    <link href="/static/tabulator-master/dist/css/tabulator.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --border-color: #e0e0e0;
            --hover-color: #f5f5f5;
            --header-bg: #f5f5f5;
        }

        body {
            background-color: #fff;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            padding: 15px;
        }

        .page-title {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .basic-settings {
            background-color: var(--header-bg);
            padding: 12px;
            border: 1px solid var(--border-color);
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .settings-container {
            display: flex;
            gap: 15px;
        }

        .settings-section {
            background-color: #fff;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }

        .settings-section:last-child {
            margin-bottom: 0;
        }

        .settings-section h4 {
            color: var(--primary-color);
            font-size: 0.95rem;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
        }

        .form-group {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .form-group label {
            flex: 0 0 100px;
            margin-right: 8px;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .form-group select,
        .form-group input {
            flex: 0 0 120px;
            font-size: 0.9rem;
            min-width: 0;
            max-width: 200px;
        }

        .form-group input[type="text"] {
            flex: 0 0 180px;
            max-width: 200px;
        }

        .pipe-info {
            background-color: #fff;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            margin-top: 10px;
        }

        .pipe-info .form-group {
            margin-bottom: 8px;
            max-width: 300px;
        }

        .pipe-info label {
            display: inline-block;
            width: 120px;
            margin-right: 10px;
        }

        .pipe-info select,
        .pipe-info input {
            display: inline-block;
            width: calc(100% - 130px);
        }

        .table-container {
            margin: 15px 0;
            overflow-x: auto;
            max-width: fit-content;
        }

        .data-table {
            width: auto;
            border-collapse: collapse;
            white-space: nowrap;
        }

        .data-table th {
            background-color: var(--header-bg);
            color: var(--primary-color);
            font-weight: 500;
            padding: 8px 12px;
            text-align: left;
            font-size: 0.9rem;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table td {
            padding: 8px 12px;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9rem;
        }

        .data-table td.editable {
            padding: 0;
        }

        .data-table td.editable input {
            width: 100%;
            height: 100%;
            border: none;
            padding: 8px 12px;
            font-size: 0.9rem;
            background: transparent;
        }

        .data-table td.editable input:focus {
            outline: 2px solid var(--primary-color);
            background: #fff;
        }

        .data-table td.readonly {
            background-color: var(--header-bg);
            color: var(--primary-color);
        }

        .data-table td.editable.number {
            text-align: right;
        }

        .data-table td.editable.number input {
            text-align: right;
        }

        .data-table tbody tr:hover {
            background-color: var(--hover-color);
        }

        .chart-container {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid var(--border-color);
        }

        .section-header {
            cursor: pointer;
            padding: 10px;
            background-color: var(--header-bg);
            color: var(--primary-color);
            margin-bottom: 10px;
            border: 1px solid var(--border-color);
        }

        .section-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .section-content {
            display: none;
        }

        .section-content.expanded {
            display: block;
        }

        .section-icon {
            float: right;
            transition: transform 0.3s;
            color: var(--primary-color);
        }

        .section-icon.collapsed {
            transform: rotate(-90deg);
        }

        .btn {
            padding: 6px 12px;
            font-size: 0.9rem;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .design-doc {
            padding: 15px;
        }

        .design-doc h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1rem;
            font-weight: 500;
        }

        .design-doc ul {
            padding-left: 20px;
            margin-bottom: 15px;
        }

        .design-doc li {
            margin-bottom: 8px;
        }

        .design-doc strong {
            color: var(--primary-color);
        }

        .bk-root {
            width: 100% !important;
        }

        .bk-root .bk-canvas {
            width: 100% !important;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .btn {
                width: 100%;
                margin-right: 0;
            }

            .data-table th,
            .data-table td {
                padding: 8px;
                font-size: 0.85rem;
            }

            .settings-container {
                flex-direction: column;
                gap: 10px;
            }
        }

        .action-icon {
            cursor: pointer;
            color: #dc3545;
            font-size: 1.1rem;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .action-icon:hover {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* 设置各列宽度 */
        .data-table th:nth-child(1), /* 位号 */
        .data-table td:nth-child(1) {
            width: 80px;
        }

        .data-table th:nth-child(2), /* 流体介质 */
        .data-table td:nth-child(2) {
            width: 100px;
        }

        .data-table th:nth-child(3), /* 管道材质 */
        .data-table td:nth-child(3) {
            width: 80px;
        }

        .data-table th:nth-child(4), /* 压力 */
        .data-table td:nth-child(4),
        .data-table th:nth-child(5), /* 温度 */
        .data-table td:nth-child(5),
        .data-table th:nth-child(6), /* 流量 */
        .data-table td:nth-child(6) {
            width: 100px;
        }

        .data-table th:nth-child(7), /* 管道规格 */
        .data-table td:nth-child(7),
        .data-table th:nth-child(8), /* 保温层厚 */
        .data-table td:nth-child(8) {
            width: 100px;
        }

        .data-table th:nth-child(9), /* 流速标准 */
        .data-table td:nth-child(9) {
            width: 100px;
        }

        .data-table th:nth-child(10), /* 操作 */
        .data-table td:nth-child(10) {
            width: 50px;
            text-align: center;
        }

        /* 材质信息样式 */
        .form-control-plaintext {
            display: inline-block;
            padding: 0.375rem 0;
            margin-bottom: 0;
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--primary-color);
            background-color: transparent;
            border: solid transparent;
            border-width: 1px 0;
            font-weight: 500;
        }

        /* 材质对比表样式 */
        .material-comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 0.85rem;
        }

        .material-comparison-table th,
        .material-comparison-table td {
            border: 1px solid var(--border-color);
            padding: 6px 8px;
            text-align: center;
        }

        .material-comparison-table th {
            background-color: var(--header-bg);
            color: var(--primary-color);
            font-weight: 500;
        }

        .material-comparison-table tbody tr:hover {
            background-color: var(--hover-color);
        }

        /* 应力曲线数据样式 */
        .stress-curve-data {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        /* 材质信息高亮 */
        .material-info-highlight {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 8px 12px;
            margin: 8px 0;
            border-radius: 0 4px 4px 0;
        }

        /* 温度范围警告样式 */
        .temp-warning {
            color: #dc3545;
            font-weight: 500;
        }

        .temp-normal {
            color: #28a745;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">压力管道计算选型</h1>

        <!-- 基础设置区域 -->
        <div class="section-header" onclick="toggleSection('basicSettingsSection')">
            <h3>基础设置 <i class="section-icon">▼</i></h3>
        </div>
        <div id="basicSettingsSection" class="section-content">
            <div class="basic-settings">
                <div class="settings-container">
                    <!-- 项目信息子区域 -->
                    <div class="settings-section">
                        <h4>项目信息</h4>
                        <div class="form-group">
                            <label for="projectName">项目名称：</label>
                            <input type="text" class="form-control" id="projectName" readonly>
                        </div>
                        <div class="form-group">
                            <label for="pressureStandard">公称压力标准：</label>
                            <select class="form-control" id="pressureStandard" onchange="handlePressureStandardChange()">
                                <option value="PN">PN系列</option>
                                <option value="CLASS">CLASS系列</option>
                            </select>
                        </div>
                        <!-- 管道系列选择 -->
                        <div class="form-group">
                            <label for="pipeSeries" class="form-label">管道系列:</label>
                            <select class="form-control" id="pipeSeries">
                                <option value="A">A系列</option>
                                <option value="B">B系列</option>
                            </select>
                        </div>
                    </div>

                    <!-- 管道位号子区域 -->
                    <div class="settings-section">
                        <h4>管道位号</h4>
                        <div class="form-group">
                            <label for="pipeMaterial">管道材质</label>
                            <select class="form-control" id="pipeMaterial" onchange="handleMaterialChange()">
                                <option value="20G">20G钢</option>
                                <option value="16MnR">16MnR钢</option>
                                <option value="15CrMoR">15CrMoR钢</option>
                                <option value="12Cr1MoVR">12Cr1MoVR钢</option>
                                <option value="304" selected>304不锈钢</option>
                                <option value="316L">316L不锈钢</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="medium">介质</label>
                            <select class="form-control" id="medium" onchange="handleMediumChange()">
                                <option value="压缩空气">压缩空气</option>
                                <option value="水">水</option>
                                <option value="航空煤油">航空煤油</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="velocityStandard">推荐流速 (m/s)</label>
                            <input type="number" class="form-control" id="velocityStandard"
                                   onchange="handleVelocityChange()" step="1">
                        </div>
                    </div>

                    <!-- 单位设置子区域 -->
                    <div class="settings-section">
                        <h4>单位设置</h4>
                        <div class="form-group">
                            <label for="pressureUnit">压力单位</label>
                            <select class="form-control" id="pressureUnit">
                                <option value="MPa">MPa</option>
                                <option value="bar">bar</option>
                                <option value="kPa">kPa</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="temperatureUnit">温度单位</label>
                            <select class="form-control" id="temperatureUnit">
                                <option value="C">°C</option>
                                <option value="K">K</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="flowUnit" class="form-label">流量单位:</label>
                            <select class="form-control" id="flowUnit" onchange="handleUnitChange()">
                                <option value="kg/h">kg/h</option>
                                <option value="kg/s" selected>kg/s</option>
                                <option value="m3/h">m³/h</option>
                                <option value="m3/s">m³/s</option>
                            </select>
                        </div>
                    </div>

                    <!-- 材质信息子区域 -->
                    <div class="settings-section">
                        <h4>材质信息</h4>
                        <div class="form-group">
                            <label>温度范围:</label>
                            <span id="materialTempRange" class="form-control-plaintext">20°C ~ 700°C</span>
                        </div>
                        <div class="form-group">
                            <label for="queryTemp">查询温度(°C):</label>
                            <input type="number" class="form-control" id="queryTemp" value="200"
                                   min="0" max="800" onchange="updateAllowableStress()">
                        </div>
                        <div class="form-group">
                            <label>许用应力:</label>
                            <span id="allowableStressDisplay" class="form-control-plaintext">138 MPa</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 材质对比工具区域 -->
        <div class="section-header" onclick="toggleSection('materialComparisonSection')">
            <h3>材质对比工具 <i class="section-icon">▼</i></h3>
        </div>
        <div id="materialComparisonSection" class="section-content">
            <div class="basic-settings">
                <div class="row">
                    <div class="col-md-6">
                        <h5>材质对比表</h5>
                        <div class="form-group">
                            <label for="comparisonTemp">对比温度(°C):</label>
                            <input type="number" class="form-control" id="comparisonTemp" value="300"
                                   min="0" max="800" onchange="generateMaterialComparison()">
                            <button class="btn btn-primary mt-2" onclick="generateMaterialComparison()">生成对比表</button>
                        </div>
                        <div id="materialComparisonTable" class="mt-3"></div>
                    </div>
                    <div class="col-md-6">
                        <h5>温度-应力曲线</h5>
                        <div class="form-group">
                            <label for="curveMaterial">选择材质:</label>
                            <select class="form-control" id="curveMaterial" onchange="generateStressCurve()">
                                <option value="20G">20G钢</option>
                                <option value="16MnR">16MnR钢</option>
                                <option value="15CrMoR">15CrMoR钢</option>
                                <option value="12Cr1MoVR">12Cr1MoVR钢</option>
                                <option value="304" selected>304不锈钢</option>
                                <option value="316L">316L不锈钢</option>
                            </select>
                            <button class="btn btn-primary mt-2" onclick="generateStressCurve()">生成曲线数据</button>
                        </div>
                        <div id="stressCurveData" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="section-header" onclick="toggleSection('tableSection')">
            <h3>数据输入区域 <i class="section-icon">▼</i></h3>
        </div>
        <div id="tableSection" class="section-content">
            <div class="table-container">
                <table class="data-table" id="dataTable">
                    <thead>
                        <tr>
                            <th>位号</th>
                            <th>流体介质</th>
                            <th>管道材质</th>
                            <th id="th-pressure">压力 (MPa)</th>
                            <th id="th-temperature">温度 (°C)</th>
                            <th id="th-flow">流量 (kg/s)</th>
                            <th>管道规格</th>
                            <th>保温层厚</th>
                            <th>流速标准</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
            <!-- 操作按钮 -->
            <div class="mt-3">
                <button class="btn btn-primary" onclick="addRow()">添加行</button>
                <button class="btn btn-success" onclick="calculate()">计算</button>
                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">导入数据</button>
                <button class="btn btn-primary" onclick="exportReport()">导出计算报告</button>
                <input type="file" id="fileInput" style="display: none" accept=".csv" onchange="importData()">
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="section-header" onclick="toggleSection('chartSection')">
            <h3>可视化图表区域 <i class="section-icon">▼</i></h3>
        </div>
        <div id="chartSection" class="section-content">
            <div class="row">
                <div class="col-md-4">
                    <div class="chart-container">
                        <div id="pressure-chart"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div id="temperature-chart"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div id="flow-chart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计说明区域 -->
        <div class="section-header" onclick="toggleSection('designSection')">
            <h3>设计说明 <i class="section-icon">▼</i></h3>
        </div>
        <div id="designSection" class="section-content">
            <div class="design-doc">
                <h4>管道设计规范说明</h4>
                <ul>
                    <li><strong>压力等级：</strong>根据GB/T 20801.2-2020标准，管道压力等级分为PN2.5、PN6、PN10、PN16、PN25、PN40、PN63、PN100等。</li>
                    <li><strong>材质选择：</strong>系统支持6种常用管道材质，包括碳钢(20G)、低合金钢(16MnR)、耐热钢(15CrMoR、12Cr1MoVR)、不锈钢(304、316L)等。</li>
                    <li><strong>许用应力：</strong>基于GB 150《压力容器》和GB/T 20801《压力管道规范》，提供各材质在不同温度下的许用应力数据。</li>
                    <li><strong>壁厚计算：</strong>采用标准壁厚计算公式：t = PD/(2σφW + PY)，其中σ为许用应力，根据材质和温度自动查表获取。</li>
                    <li><strong>流速限制：</strong>一般液体流速控制在1-3m/s，气体流速控制在10-30m/s，具体根据介质特性确定。</li>
                </ul>

                <h4>材质许用应力功能</h4>
                <ul>
                    <li><strong>智能查表：</strong>根据选择的材质和输入温度，自动查询对应的许用应力值</li>
                    <li><strong>线性插值：</strong>对于数据表中间温度点，采用线性插值确保计算精度</li>
                    <li><strong>边界处理：</strong>超出材质温度范围时，自动使用边界值并给出提示</li>
                    <li><strong>材质对比：</strong>提供材质对比工具，帮助选择最适合的材质</li>
                    <li><strong>温度范围：</strong>20G(20-500°C)、16MnR(20-500°C)、15CrMoR(20-600°C)、12Cr1MoVR(20-600°C)、304(20-700°C)、316L(20-700°C)</li>
                </ul>

                <h4>计算依据</h4>
                <ul>
                    <li><strong>流量计算：</strong>基于连续性方程和伯努利方程</li>
                    <li><strong>压力损失：</strong>考虑沿程阻力和局部阻力</li>
                    <li><strong>温度影响：</strong>精确考虑介质温度对管道材料许用应力的影响</li>
                    <li><strong>安全系数：</strong>许用应力数据已包含安全系数，可直接用于设计计算</li>
                    <li><strong>标准依据：</strong>GB 150、GB/T 20801、相关材质技术标准</li>
                </ul>

                <h4>使用说明</h4>
                <ul>
                    <li><strong>材质选择：</strong>在基础设置中选择合适的管道材质</li>
                    <li><strong>温度查询：</strong>可在材质信息区域查询指定温度下的许用应力</li>
                    <li><strong>自动计算：</strong>系统根据材质和温度自动计算推荐管径和壁厚</li>
                    <li><strong>材质对比：</strong>使用材质对比工具比较不同材质的性能</li>
                    <li><strong>结果导出：</strong>可导出包含材质信息的完整计算报告</li>
                    <li><strong>图表分析：</strong>查看温度-应力关系曲线，辅助材质选择</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 导入数据模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="file" class="form-control" id="fileInput" accept=".csv">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="importData()">导入</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/bootstrap-4.6.0-dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/bokeh/bokeh.min.js"></script>
    <script src="/static/bokeh/bokeh-widgets.min.js"></script>
    <script src="/static/bokeh/bokeh-tables.min.js"></script>
    <script src="/static/bokeh/bokeh-api.min.js"></script>
    <script type="text/javascript" src="/static/tabulator-master/dist/js/tabulator.min.js"></script>
    <script src="/static/js/pipe_calculator.js"></script>

    <script>
        // 材质变更处理函数
        function handleMaterialChange() {
            updateMaterialInfo();
            updateAllowableStress();
        }

        // 更新材质信息显示
        function updateMaterialInfo() {
            const material = document.getElementById('pipeMaterial').value;
            const range = getMaterialTemperatureRange(material);

            if (range) {
                document.getElementById('materialTempRange').textContent = `${range.min}°C ~ ${range.max}°C`;
            } else {
                document.getElementById('materialTempRange').textContent = '数据不可用';
            }
        }

        // 更新许用应力显示
        function updateAllowableStress() {
            const material = document.getElementById('pipeMaterial').value;
            const temperature = parseFloat(document.getElementById('queryTemp').value) || 200;

            try {
                const stress = getAllowableStress(material, temperature);
                const range = getMaterialTemperatureRange(material);

                let displayText = `${stress} MPa`;
                let className = 'temp-normal';

                if (range) {
                    if (temperature < range.min || temperature > range.max) {
                        displayText += ' (超出范围)';
                        className = 'temp-warning';
                    }
                }

                const stressDisplay = document.getElementById('allowableStressDisplay');
                stressDisplay.textContent = displayText;
                stressDisplay.className = `form-control-plaintext ${className}`;

            } catch (error) {
                document.getElementById('allowableStressDisplay').textContent = '计算错误';
            }
        }

        // 生成材质对比表
        function generateMaterialComparison() {
            const temperature = parseFloat(document.getElementById('comparisonTemp').value) || 300;
            const materials = getAvailableMaterials();

            let tableHTML = '<table class="material-comparison-table">';
            tableHTML += '<thead><tr><th>材质</th><th>温度范围(°C)</th><th>许用应力(MPa)</th><th>状态</th></tr></thead>';
            tableHTML += '<tbody>';

            materials.forEach(material => {
                const range = getMaterialTemperatureRange(material);
                const stress = getAllowableStress(material, temperature);

                let status = '正常';
                let statusClass = 'temp-normal';

                if (temperature < range.min) {
                    status = '低于最低温度';
                    statusClass = 'temp-warning';
                } else if (temperature > range.max) {
                    status = '高于最高温度';
                    statusClass = 'temp-warning';
                }

                tableHTML += `<tr>`;
                tableHTML += `<td><strong>${material}</strong></td>`;
                tableHTML += `<td>${range.min} ~ ${range.max}</td>`;
                tableHTML += `<td>${stress}</td>`;
                tableHTML += `<td class="${statusClass}">${status}</td>`;
                tableHTML += `</tr>`;
            });

            tableHTML += '</tbody></table>';
            document.getElementById('materialComparisonTable').innerHTML = tableHTML;
        }

        // 生成应力曲线数据
        function generateStressCurve() {
            const material = document.getElementById('curveMaterial').value;
            const range = getMaterialTemperatureRange(material);

            if (!range) {
                document.getElementById('stressCurveData').innerHTML = '<p>材质数据不可用</p>';
                return;
            }

            let curveData = `${material} 材质温度-应力关系数据:\n`;
            curveData += `温度范围: ${range.min}°C ~ ${range.max}°C\n\n`;
            curveData += `温度(°C)  许用应力(MPa)\n`;
            curveData += `------------------------\n`;

            const step = Math.max(10, Math.floor((range.max - range.min) / 20));

            for (let temp = range.min; temp <= range.max; temp += step) {
                const stress = getAllowableStress(material, temp);
                curveData += `${temp.toString().padStart(6)}    ${stress.toString().padStart(8)}\n`;
            }

            // 确保包含最高温度点
            if ((range.max - range.min) % step !== 0) {
                const stress = getAllowableStress(material, range.max);
                curveData += `${range.max.toString().padStart(6)}    ${stress.toString().padStart(8)}\n`;
            }

            curveData += `\n数据点总数: ${Math.ceil((range.max - range.min) / step) + 1}`;

            document.getElementById('stressCurveData').innerHTML = `<div class="stress-curve-data">${curveData}</div>`;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 原有的初始化代码保持不变

            // 新增的材质功能初始化
            updateMaterialInfo();
            updateAllowableStress();
            generateMaterialComparison();
            generateStressCurve();

            // 默认收起材质对比区域
            const materialSection = document.getElementById('materialComparisonSection');
            if (materialSection) {
                materialSection.classList.remove('expanded');
                const icon = materialSection.previousElementSibling.querySelector('.section-icon');
                if (icon) icon.classList.add('collapsed');
            }
        });
    </script>
</body>

</html>