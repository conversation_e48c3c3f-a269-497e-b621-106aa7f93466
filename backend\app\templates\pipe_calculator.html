<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管道计算选型工具</title>
    <link href="/static/bootstrap-4.6.0-dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/bokeh/bokeh.min.css" rel="stylesheet">
    <link href="/static/bokeh/bokeh-widgets.min.css" rel="stylesheet">
    <link href="/static/bokeh/bokeh-tables.min.css" rel="stylesheet">
    <link href="/static/tabulator-master/dist/css/tabulator.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --border-color: #e0e0e0;
            --hover-color: #f5f5f5;
            --header-bg: #f5f5f5;
        }

        body {
            background-color: #fff;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            padding: 15px;
        }

        .page-title {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .basic-settings {
            background-color: var(--header-bg);
            padding: 12px;
            border: 1px solid var(--border-color);
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .settings-container {
            display: flex;
            gap: 15px;
        }

        .settings-section {
            background-color: #fff;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }

        .settings-section:last-child {
            margin-bottom: 0;
        }

        .settings-section h4 {
            color: var(--primary-color);
            font-size: 0.95rem;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
        }

        .form-group {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .form-group label {
            flex: 0 0 100px;
            margin-right: 8px;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .form-group select,
        .form-group input {
            flex: 0 0 120px;
            font-size: 0.9rem;
            min-width: 0;
            max-width: 200px;
        }

        .form-group input[type="text"] {
            flex: 0 0 180px;
            max-width: 200px;
        }

        .pipe-info {
            background-color: #fff;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            margin-top: 10px;
        }

        .pipe-info .form-group {
            margin-bottom: 8px;
            max-width: 300px;
        }

        .pipe-info label {
            display: inline-block;
            width: 120px;
            margin-right: 10px;
        }

        .pipe-info select,
        .pipe-info input {
            display: inline-block;
            width: calc(100% - 130px);
        }

        .table-container {
            margin: 15px 0;
            overflow-x: auto;
            max-width: 100%;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        /* Tabulator自定义样式 */
        .tabulator {
            font-size: 0.9rem;
            border: none;
        }

        .tabulator .tabulator-header {
            background-color: var(--header-bg);
            border-bottom: 2px solid var(--border-color);
        }

        .tabulator .tabulator-header .tabulator-col {
            background-color: var(--header-bg);
            border-right: 1px solid var(--border-color);
        }

        .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title {
            color: var(--primary-color);
            font-weight: 500;
        }

        .tabulator .tabulator-tableholder .tabulator-table .tabulator-row {
            border-bottom: 1px solid var(--border-color);
        }

        .tabulator .tabulator-tableholder .tabulator-table .tabulator-row:hover {
            background-color: var(--hover-color);
        }

        .tabulator .tabulator-tableholder .tabulator-table .tabulator-row .tabulator-cell {
            border-right: 1px solid var(--border-color);
            padding: 8px 12px;
        }

        .tabulator .tabulator-tableholder .tabulator-table .tabulator-row.tabulator-row-even {
            background-color: #fafafa;
        }

        /* 计算状态样式 */
        .status-calculated {
            background-color: #d4edda !important;
            color: #155724;
        }

        .status-error {
            background-color: #f8d7da !important;
            color: #721c24;
        }

        .status-pending {
            background-color: #fff3cd !important;
            color: #856404;
        }

        /* 选中行样式 */
        .tabulator .tabulator-tableholder .tabulator-table .tabulator-row.tabulator-selected {
            background-color: #e3f2fd !important;
        }

        .tabulator .tabulator-tableholder .tabulator-table .tabulator-row.tabulator-selected:hover {
            background-color: #bbdefb !important;
        }

        /* 批量设置模态框样式 */
        .modal-body .form-check {
            margin-bottom: 0.5rem;
        }

        .modal-body .form-check-label {
            font-weight: 500;
            color: var(--primary-color);
        }

        .modal-body .form-control:disabled {
            background-color: #f8f9fa;
            opacity: 0.6;
        }

        .modal-body .alert {
            border-radius: 0.375rem;
            font-size: 0.9rem;
        }

        /* 隐藏数字输入框的上下调整箭头 */
        input[type="number"]::-webkit-outer-spin-button,
        input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }

        /* Tabulator编辑器中的数字输入框也隐藏箭头 */
        .tabulator-edit-box input[type="number"]::-webkit-outer-spin-button,
        .tabulator-edit-box input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .tabulator-edit-box input[type="number"] {
            -moz-appearance: textfield;
        }



        .chart-container {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid var(--border-color);
        }

        .section-header {
            cursor: pointer;
            padding: 10px;
            background-color: var(--header-bg);
            color: var(--primary-color);
            margin-bottom: 10px;
            border: 1px solid var(--border-color);
        }

        .section-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .section-content {
            display: none;
        }

        .section-content.expanded {
            display: block;
        }

        .section-icon {
            float: right;
            transition: transform 0.3s;
            color: var(--primary-color);
        }

        .section-icon.collapsed {
            transform: rotate(-90deg);
        }

        .btn {
            padding: 6px 12px;
            font-size: 0.9rem;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .design-doc {
            padding: 15px;
        }

        .design-doc h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1rem;
            font-weight: 500;
        }

        .design-doc ul {
            padding-left: 20px;
            margin-bottom: 15px;
        }

        .design-doc li {
            margin-bottom: 8px;
        }

        .design-doc strong {
            color: var(--primary-color);
        }

        .bk-root {
            width: 100% !important;
        }

        .bk-root .bk-canvas {
            width: 100% !important;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .btn {
                width: 100%;
                margin-right: 0;
            }

            .tabulator .tabulator-tableholder .tabulator-table .tabulator-row .tabulator-cell {
                padding: 6px 8px;
                font-size: 0.85rem;
            }

            .settings-container {
                flex-direction: column;
                gap: 10px;
            }
        }

        .action-icon {
            cursor: pointer;
            color: #dc3545;
            font-size: 1.1rem;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .action-icon:hover {
            background-color: #f8d7da;
            color: #721c24;
        }



        /* 材质信息样式 */
        .form-control-plaintext {
            display: inline-block;
            padding: 0.375rem 0;
            margin-bottom: 0;
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--primary-color);
            background-color: transparent;
            border: solid transparent;
            border-width: 1px 0;
            font-weight: 500;
        }

        /* 材质对比表样式 */
        .material-comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 0.85rem;
        }

        .material-comparison-table th,
        .material-comparison-table td {
            border: 1px solid var(--border-color);
            padding: 6px 8px;
            text-align: center;
        }

        .material-comparison-table th {
            background-color: var(--header-bg);
            color: var(--primary-color);
            font-weight: 500;
        }

        .material-comparison-table tbody tr:hover {
            background-color: var(--hover-color);
        }

        /* 应力曲线数据样式 */
        .stress-curve-data {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        /* 材质信息高亮 */
        .material-info-highlight {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 8px 12px;
            margin: 8px 0;
            border-radius: 0 4px 4px 0;
        }

        /* 温度范围警告样式 */
        .temp-warning {
            color: #dc3545;
            font-weight: 500;
        }

        .temp-normal {
            color: #28a745;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">压力管道计算选型</h1>

        <!-- 基础设置区域 -->
        <div class="section-header" onclick="toggleSection('basicSettingsSection')">
            <h3>基础设置 <i class="section-icon">▼</i></h3>
        </div>
        <div id="basicSettingsSection" class="section-content">
            <div class="basic-settings">
                <div class="settings-container">
                    <!-- 项目信息子区域 -->
                    <div class="settings-section">
                        <h4>项目信息</h4>
                        <div class="form-group">
                            <label for="projectName">项目名称：</label>
                            <input type="text" class="form-control" id="projectName" readonly>
                        </div>
                        <div class="form-group">
                            <label for="pressureStandard">公称压力标准：</label>
                            <select class="form-control" id="pressureStandard" onchange="handlePressureStandardChange()">
                                <option value="PN">PN系列</option>
                                <option value="CLASS">CLASS系列</option>
                            </select>
                        </div>
                        <!-- 管道系列选择 -->
                        <div class="form-group">
                            <label for="pipeSeries" class="form-label">管道系列:</label>
                            <select class="form-control" id="pipeSeries">
                                <option value="A">A系列</option>
                                <option value="B">B系列</option>
                            </select>
                        </div>
                    </div>

                    
                    <!-- 单位设置子区域 -->
                    <div class="settings-section">
                        <h4>单位设置</h4>
                        <div class="form-group">
                            <label for="pressureUnit">压力单位</label>
                            <select class="form-control" id="pressureUnit">
                                <option value="MPa">MPa</option>
                                <option value="bar">bar</option>
                                <option value="kPa">kPa</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="temperatureUnit">温度单位</label>
                            <select class="form-control" id="temperatureUnit">
                                <option value="C">°C</option>
                                <option value="K">K</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="flowUnit" class="form-label">流量单位:</label>
                            <select class="form-control" id="flowUnit" onchange="handleUnitChange()">
                                <option value="kg/h">kg/h</option>
                                <option value="kg/s" selected>kg/s</option>
                                <option value="m3/h">m³/h</option>
                                <option value="m3/s">m³/s</option>
                            </select>
                        </div>
                    </div>

                    <!-- 材质信息子区域 -->
                    <div class="settings-section">
                        <h4>材质信息</h4>
                        <div class="form-group">
                            <label>温度范围:</label>
                            <span id="materialTempRange" class="form-control-plaintext">20°C ~ 700°C</span>
                        </div>
                        <div class="form-group">
                            <label for="queryTemp">查询温度(°C):</label>
                            <input type="number" class="form-control" id="queryTemp" value="200"
                                   min="0" max="800" onchange="updateAllowableStress()">
                        </div>
                        <div class="form-group">
                            <label>许用应力:</label>
                            <span id="allowableStressDisplay" class="form-control-plaintext">138 MPa</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="section-header" onclick="toggleSection('tableSection')">
            <h3>数据输入区域 <i class="section-icon">▼</i></h3>
        </div>
        <div id="tableSection" class="section-content">
            <!-- 操作按钮 -->
            <div class="mb-3">
                <div class="row">
                    <div class="col-md-8">
                        <button class="btn btn-primary" onclick="addRow()">添加行</button>
                        <button class="btn btn-success" onclick="batchCalculate()">批量计算</button>
                        <button class="btn btn-info" onclick="document.getElementById('csvFileInput').click()">导入CSV</button>
                        <button class="btn btn-outline-info" onclick="downloadCSVTemplate()">下载CSV模板</button>
                        <button class="btn btn-warning" onclick="exportToExcel()">导出Excel</button>
                        <input type="file" id="csvFileInput" style="display: none" accept=".csv" onchange="importCSVData(event)">
                    </div>
                    <div class="col-md-4 text-right">
                        <button class="btn btn-outline-primary" onclick="selectAllRows()" title="Ctrl+A">全选</button>
                        <button class="btn btn-outline-secondary" onclick="deselectAllRows()" title="Ctrl+D">取消选择</button>
                        <button class="btn btn-outline-danger" onclick="deleteSelectedRows()" title="Delete">删除选中</button>
                        <button class="btn btn-outline-warning" onclick="showBatchSetModal()" id="batchSetBtn" disabled title="Ctrl+B">批量设置</button>
                    </div>
                </div>
            </div>

            <!-- 表格操作栏 -->
            <div class="d-flex justify-content-end align-items-center mb-2">
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-secondary" onclick="undoLastAction()" id="undoBtn" disabled title="撤销 (Ctrl+Z)">
                        <i class="fas fa-undo"></i> 撤销
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="redoLastAction()" id="redoBtn" disabled title="重做 (Ctrl+Y)">
                        <i class="fas fa-redo"></i> 重做
                    </button>
                </div>
            </div>

            <!-- Tabulator表格容器 -->
            <div id="pipeDataTable" class="table-container"></div>

            <!-- 使用提示 -->
            <div class="mt-3">
                <div class="alert alert-light border" role="alert">
                    <h6 class="alert-heading"><i class="fas fa-info-circle text-info"></i> 功能使用说明</h6>

                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-square text-primary"></i> 行选择功能</h6>
                            <ul class="small mb-3">
                                <li>点击行首复选框选择单行或多行</li>
                                <li>支持<kbd>Ctrl+A</kbd>全选，<kbd>Ctrl+D</kbd>取消选择</li>
                                <li>选中行会以蓝色背景高亮显示</li>
                            </ul>

                            <h6><i class="fas fa-sync-alt text-success"></i> 智能同步</h6>
                            <ul class="small mb-3">
                                <li>选中多行后，编辑其中任意单元格</li>
                                <li>系统自动将相同值同步到其他选中行</li>
                                <li>支持流体介质、材质、压力、温度、流量、流速</li>
                            </ul>
                        </div>

                        <div class="col-md-6">
                            <h6><i class="fas fa-cogs text-warning"></i> 批量设置功能</h6>
                            <ul class="small mb-3">
                                <li><strong>步骤1：</strong>选择要设置的行（勾选复选框）</li>
                                <li><strong>步骤2：</strong>点击"批量设置"按钮打开设置窗口</li>
                                <li><strong>步骤3：</strong>勾选要修改的参数并输入新值</li>
                                <li><strong>步骤4：</strong>点击"应用设置"完成批量修改</li>
                                <li><strong>提示：</strong>可点击"从首行获取值"快速填充参数</li>
                            </ul>

                            <h6><i class="fas fa-undo text-secondary"></i> 撤销重做</h6>
                            <ul class="small mb-0">
                                <li>支持撤销/重做所有编辑操作</li>
                                <li><kbd>Ctrl+Z</kbd>撤销，<kbd>Ctrl+Y</kbd>重做</li>
                                <li>包括单行编辑、智能同步、批量设置</li>
                            </ul>
                        </div>
                    </div>

                    <hr class="my-2">
                    <div class="text-center">
                        <small class="text-muted">
                            <strong>快捷键：</strong>
                            <kbd>Ctrl+A</kbd>全选 | <kbd>Ctrl+D</kbd>取消选择 | <kbd>Ctrl+B</kbd>批量设置 |
                            <kbd>Delete</kbd>删除选中行 | <kbd>Ctrl+Z</kbd>撤销 | <kbd>Ctrl+Y</kbd>重做
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="section-header" onclick="toggleSection('chartSection')">
            <h3>可视化图表区域 <i class="section-icon">▼</i></h3>
        </div>
        <div id="chartSection" class="section-content">
            <div class="row">
                <div class="col-md-4">
                    <div class="chart-container">
                        <div id="pressure-chart"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div id="temperature-chart"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div id="flow-chart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计说明区域 -->
        <div class="section-header" onclick="toggleSection('designSection')">
            <h3>设计说明 <i class="section-icon">▼</i></h3>
        </div>
        <div id="designSection" class="section-content">
            <div class="design-doc">
                <h4>管道设计规范说明</h4>
                <ul>
                    <li><strong>压力等级：</strong>根据GB/T 20801.2-2020标准，管道压力等级分为PN2.5、PN6、PN10、PN16、PN25、PN40、PN63、PN100等。</li>
                    <li><strong>材质选择：</strong>系统支持6种常用管道材质，包括碳钢(20G)、低合金钢(16MnR)、耐热钢(15CrMoR、12Cr1MoVR)、不锈钢(304、316L)等。</li>
                    <li><strong>许用应力：</strong>基于GB 150《压力容器》和GB/T 20801《压力管道规范》，提供各材质在不同温度下的许用应力数据。</li>
                    <li><strong>壁厚计算：</strong>采用标准壁厚计算公式：t = PD/(2σφW + PY)，其中σ为许用应力，根据材质和温度自动查表获取。</li>
                    <li><strong>流速限制：</strong>一般液体流速控制在1-3m/s，气体流速控制在10-30m/s，具体根据介质特性确定。</li>
                </ul>

                <h4>材质许用应力功能</h4>
                <ul>
                    <li><strong>智能查表：</strong>根据选择的材质和输入温度，自动查询对应的许用应力值</li>
                    <li><strong>线性插值：</strong>对于数据表中间温度点，采用线性插值确保计算精度</li>
                    <li><strong>边界处理：</strong>超出材质温度范围时，自动使用边界值并给出提示</li>
                    <li><strong>材质对比：</strong>提供材质对比工具，帮助选择最适合的材质</li>
                    <li><strong>温度范围：</strong>20G(20-500°C)、16MnR(20-500°C)、15CrMoR(20-600°C)、12Cr1MoVR(20-600°C)、304(20-700°C)、316L(20-700°C)</li>
                </ul>

                <h4>计算依据</h4>
                <ul>
                    <li><strong>流量计算：</strong>基于连续性方程和伯努利方程</li>
                    <li><strong>压力损失：</strong>考虑沿程阻力和局部阻力</li>
                    <li><strong>温度影响：</strong>精确考虑介质温度对管道材料许用应力的影响</li>
                    <li><strong>安全系数：</strong>许用应力数据已包含安全系数，可直接用于设计计算</li>
                    <li><strong>标准依据：</strong>GB 150、GB/T 20801、相关材质技术标准</li>
                </ul>

                <h4>使用说明</h4>
                <ul>
                    <li><strong>材质选择：</strong>在基础设置中选择合适的管道材质</li>
                    <li><strong>温度查询：</strong>可在材质信息区域查询指定温度下的许用应力</li>
                    <li><strong>自动计算：</strong>系统根据材质和温度自动计算推荐管径和壁厚</li>
                    <li><strong>材质对比：</strong>使用材质对比工具比较不同材质的性能</li>
                    <li><strong>结果导出：</strong>可导出包含材质信息的完整计算报告</li>
                    <li><strong>图表分析：</strong>查看温度-应力关系曲线，辅助材质选择</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 导入数据模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="file" class="form-control" id="fileInput" accept=".csv">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="importData()">导入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量设置模态框 -->
    <div class="modal fade" id="batchSetModal" tabindex="-1" role="dialog" aria-labelledby="batchSetModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchSetModalLabel">批量设置参数</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        已选择 <span id="selectedRowCount">0</span> 行数据。只有勾选的参数会被批量设置。
                        <button type="button" class="btn btn-sm btn-outline-primary float-right" onclick="fillFromFirstSelected()">
                            <i class="fas fa-copy"></i> 从首行获取值
                        </button>
                    </div>

                    <form id="batchSetForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="setBatchMedium">
                                        <label class="form-check-label" for="setBatchMedium">
                                            <strong>流体介质</strong>
                                        </label>
                                    </div>
                                    <select class="form-control" id="batchMedium" disabled>
                                        <option value="压缩空气">压缩空气</option>
                                        <option value="水">水</option>
                                        <option value="航空煤油">航空煤油</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="setBatchMaterial">
                                        <label class="form-check-label" for="setBatchMaterial">
                                            <strong>管道材质</strong>
                                        </label>
                                    </div>
                                    <select class="form-control" id="batchMaterial" disabled>
                                        <option value="20G">20G</option>
                                        <option value="16MnR">16MnR</option>
                                        <option value="15CrMoR">15CrMoR</option>
                                        <option value="12Cr1MoVR">12Cr1MoVR</option>
                                        <option value="321">321</option>
                                        <option value="304">304</option>
                                        <option value="316L">316L</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="setBatchPressure">
                                        <label class="form-check-label" for="setBatchPressure">
                                            <strong>压力 (MPa)</strong>
                                        </label>
                                    </div>
                                    <input type="number" class="form-control" id="batchPressure" min="0.1" max="50" step="0.1" value="1.0" disabled>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="setBatchTemperature">
                                        <label class="form-check-label" for="setBatchTemperature">
                                            <strong>温度 (°C)</strong>
                                        </label>
                                    </div>
                                    <input type="number" class="form-control" id="batchTemperature" min="-50" max="850" step="1" value="20" disabled>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="setBatchFlow">
                                        <label class="form-check-label" for="setBatchFlow">
                                            <strong>流量 (kg/s)</strong>
                                        </label>
                                    </div>
                                    <input type="number" class="form-control" id="batchFlow" min="0.01" max="1000" step="0.01" value="5.0" disabled>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="setBatchVelocity">
                                        <label class="form-check-label" for="setBatchVelocity">
                                            <strong>推荐流速 (m/s)</strong>
                                        </label>
                                    </div>
                                    <input type="number" class="form-control" id="batchVelocity" min="0.1" max="100" step="0.1" value="50.0" disabled>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="applyBatchSettings()">应用设置</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/bootstrap-4.6.0-dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/bokeh/bokeh.min.js"></script>
    <script src="/static/bokeh/bokeh-widgets.min.js"></script>
    <script src="/static/bokeh/bokeh-tables.min.js"></script>
    <script src="/static/bokeh/bokeh-api.min.js"></script>
    <script type="text/javascript" src="/static/tabulator-master/dist/js/tabulator.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="/static/js/pipe_calculator.js"></script>

    <script>
        // 材质变更处理函数
        function handleMaterialChange() {
            updateMaterialInfo();
            updateAllowableStress();
        }

        // 更新材质信息显示
        function updateMaterialInfo() {
            const material = document.getElementById('pipeMaterial').value;
            const range = getMaterialTemperatureRange(material);

            if (range) {
                document.getElementById('materialTempRange').textContent = `${range.min}°C ~ ${range.max}°C`;
            } else {
                document.getElementById('materialTempRange').textContent = '数据不可用';
            }
        }

        // 更新许用应力显示
        function updateAllowableStress() {
            const material = document.getElementById('pipeMaterial').value;
            const temperature = parseFloat(document.getElementById('queryTemp').value) || 200;

            try {
                const stress = getAllowableStress(material, temperature);
                const range = getMaterialTemperatureRange(material);

                let displayText = `${stress} MPa`;
                let className = 'temp-normal';

                if (range) {
                    if (temperature < range.min || temperature > range.max) {
                        displayText += ' (超出范围)';
                        className = 'temp-warning';
                    }
                }

                const stressDisplay = document.getElementById('allowableStressDisplay');
                stressDisplay.textContent = displayText;
                stressDisplay.className = `form-control-plaintext ${className}`;

            } catch (error) {
                document.getElementById('allowableStressDisplay').textContent = '计算错误';
            }
        }

        // 生成材质对比表
        function generateMaterialComparison() {
            const temperature = parseFloat(document.getElementById('comparisonTemp').value) || 300;
            const materials = getAvailableMaterials();

            let tableHTML = '<table class="material-comparison-table">';
            tableHTML += '<thead><tr><th>材质</th><th>温度范围(°C)</th><th>许用应力(MPa)</th><th>状态</th></tr></thead>';
            tableHTML += '<tbody>';

            materials.forEach(material => {
                const range = getMaterialTemperatureRange(material);
                const stress = getAllowableStress(material, temperature);

                let status = '正常';
                let statusClass = 'temp-normal';

                if (temperature < range.min) {
                    status = '低于最低温度';
                    statusClass = 'temp-warning';
                } else if (temperature > range.max) {
                    status = '高于最高温度';
                    statusClass = 'temp-warning';
                }

                tableHTML += `<tr>`;
                tableHTML += `<td><strong>${material}</strong></td>`;
                tableHTML += `<td>${range.min} ~ ${range.max}</td>`;
                tableHTML += `<td>${stress}</td>`;
                tableHTML += `<td class="${statusClass}">${status}</td>`;
                tableHTML += `</tr>`;
            });

            tableHTML += '</tbody></table>';
            document.getElementById('materialComparisonTable').innerHTML = tableHTML;
        }

        // 生成应力曲线数据
        function generateStressCurve() {
            const material = document.getElementById('curveMaterial').value;
            const range = getMaterialTemperatureRange(material);

            if (!range) {
                document.getElementById('stressCurveData').innerHTML = '<p>材质数据不可用</p>';
                return;
            }

            let curveData = `${material} 材质温度-应力关系数据:\n`;
            curveData += `温度范围: ${range.min}°C ~ ${range.max}°C\n\n`;
            curveData += `温度(°C)  许用应力(MPa)\n`;
            curveData += `------------------------\n`;

            const step = Math.max(10, Math.floor((range.max - range.min) / 20));

            for (let temp = range.min; temp <= range.max; temp += step) {
                const stress = getAllowableStress(material, temp);
                curveData += `${temp.toString().padStart(6)}    ${stress.toString().padStart(8)}\n`;
            }

            // 确保包含最高温度点
            if ((range.max - range.min) % step !== 0) {
                const stress = getAllowableStress(material, range.max);
                curveData += `${range.max.toString().padStart(6)}    ${stress.toString().padStart(8)}\n`;
            }

            curveData += `\n数据点总数: ${Math.ceil((range.max - range.min) / step) + 1}`;

            document.getElementById('stressCurveData').innerHTML = `<div class="stress-curve-data">${curveData}</div>`;
        }

        // 全局变量
        let pipeTable;
        let rowIdCounter = 1;
        let historyStack = [];
        let historyIndex = -1;
        const maxHistorySize = 50;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Tabulator表格
            initializePipeTable();

            // 新增的材质功能初始化
            updateMaterialInfo();
            updateAllowableStress();
            generateMaterialComparison();
            generateStressCurve();

            // 默认收起材质对比区域
            const materialSection = document.getElementById('materialComparisonSection');
            if (materialSection) {
                materialSection.classList.remove('expanded');
                const icon = materialSection.previousElementSibling.querySelector('.section-icon');
                if (icon) icon.classList.add('collapsed');
            }
        });

        // 初始化Tabulator表格
        function initializePipeTable() {
            pipeTable = new Tabulator("#pipeDataTable", {
                height: "400px",
                layout: "fitColumns",
                resizableColumns: true,
                movableColumns: true,
                addRowPos: "bottom",
                history: true,
                pagination: false,
                responsiveLayout: "hide",
                placeholder: "暂无数据，点击'添加行'开始输入数据",
                selectable: true, // 启用行选择
                selectableRangeMode: "click", // 支持范围选择
                selectableRollingSelection: false, // 禁用滚动选择
                selectablePersistence: false, // 不持久化选择状态
                columns: [
                    {
                        formatter: "rowSelection",
                        titleFormatter: "rowSelection",
                        hozAlign: "center",
                        headerSort: false,
                        width: 40,
                        cellClick: function(e, cell) {
                            cell.getRow().toggleSelect();
                        }
                    },
                    {
                        title: "行号",
                        field: "rowNumber",
                        width: 60,
                        hozAlign: "center",
                        headerHozAlign: "center",
                        formatter: function(cell) {
                            return cell.getRow().getPosition();
                        },
                        headerSort: false
                    },
                    {
                        title: "流体介质",
                        field: "medium",
                        width: 120,
                        headerHozAlign: "center",
                        editor: "select",
                        editorParams: {
                            values: ["压缩空气", "水", "航空煤油"]
                        },
                        validator: "required"
                    },
                    {
                        title: "管道材质",
                        field: "material",
                        width: 100,
                        headerHozAlign: "center",
                        editor: "select",
                        editorParams: {
                            values: ["20G", "16MnR", "15CrMoR", "12Cr1MoVR", "321", "304", "316L"]
                        },
                        validator: "required"
                    },
                    {
                        title: "压力 (MPa)",
                        field: "pressure",
                        width: 120,
                        editor: "number",
                        editorParams: {min: 0.1, max: 50, step: 0.1},
                        validator: ["required", "min:0.1"],
                        formatter: function(cell) {
                            return parseFloat(cell.getValue()).toFixed(2);
                        }
                    },
                    {
                        title: "温度 (°C)",
                        field: "temperature",
                        width: 120,
                        editor: "number",
                        editorParams: {min: -50, max: 850, step: 1},
                        validator: ["required", "min:-50", "max:850"],
                        formatter: function(cell) {
                            return parseInt(cell.getValue());
                        }
                    },
                    {
                        title: "流量 (kg/s)",
                        field: "flow",
                        width: 120,
                        editor: "number",
                        editorParams: {min: 0.01, max: 1000, step: 0.01},
                        validator: ["required", "min:0.01"],
                        formatter: function(cell) {
                            return parseFloat(cell.getValue()).toFixed(2);
                        }
                    },
                    {
                        title: "推荐流速 (m/s)",
                        field: "velocity",
                        width: 140,
                        editor: "number",
                        editorParams: {min: 0.1, max: 100, step: 0.1},
                        validator: ["required", "min:0.1"],
                        formatter: function(cell) {
                            return parseFloat(cell.getValue()).toFixed(1);
                        }
                    },
                    {
                        title: "计算内径 (mm)",
                        field: "calcDiameter",
                        width: 140,
                        formatter: function(cell) {
                            const value = cell.getValue();
                            return value ? parseFloat(value).toFixed(2) : "-";
                        }
                    },
                    {
                        title: "许用应力 (MPa)",
                        field: "allowableStress",
                        width: 140,
                        formatter: function(cell) {
                            const value = cell.getValue();
                            return value ? parseFloat(value).toFixed(2) : "-";
                        }
                    },
                    {
                        title: "管道规格",
                        field: "pipeSpec",
                        width: 120,
                        formatter: function(cell) {
                            return cell.getValue() || "-";
                        }
                    },
                    {
                        title: "状态",
                        field: "status",
                        width: 80,
                        formatter: function(cell) {
                            const status = cell.getValue() || "待计算";
                            const statusClass = {
                                "已计算": "status-calculated",
                                "错误": "status-error",
                                "待计算": "status-pending"
                            };
                            return `<span class="${statusClass[status] || 'status-pending'}">${status}</span>`;
                        }
                    },
                    {
                        title: "操作",
                        field: "actions",
                        width: 120,
                        hozAlign: "center",
                        formatter: function(cell) {
                            const rowIndex = cell.getRow().getIndex();
                            return `
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="calculateSingleRow(${rowIndex})" title="计算">
                                        <i class="fas fa-calculator"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteRow(${rowIndex})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            `;
                        }
                    }
                ],
                data: [
                    {
                        id: rowIdCounter++,
                        medium: "压缩空气",
                        material: "304",
                        pressure: 1.0,
                        temperature: 20,
                        flow: 5.0,
                        velocity: 50.0,
                        status: "待计算"
                    }
                ]
            });

            // 监听数据变化
            pipeTable.on("dataChanged", function(data) {
                // 数据变化时的处理（如果需要）
            });

            // 监听单元格编辑
            pipeTable.on("cellEdited", function(cell) {
                const editedRow = cell.getRow();
                const field = cell.getField();
                const newValue = cell.getValue();
                const oldValue = cell.getOldValue();

                // 获取所有选中的行
                const selectedRows = pipeTable.getSelectedRows();

                // 如果编辑的行在选中行中，且有其他选中行，则启用智能同步
                if (selectedRows.length > 1 &&
                    selectedRows.includes(editedRow) &&
                    ['medium', 'material', 'pressure', 'temperature', 'flow', 'velocity'].includes(field)) {

                    // 记录操作前的状态用于撤销
                    const beforeState = selectedRows.map(row => ({
                        row: row,
                        data: {...row.getData()}
                    }));

                    // 准备更新数据
                    const updateData = {
                        [field]: newValue,
                        // 清除计算结果
                        calcDiameter: null,
                        allowableStress: null,
                        pipeSpec: null,
                        status: "待计算"
                    };

                    // 更新其他选中行（排除当前编辑的行）
                    const updatedRows = [];
                    selectedRows.forEach(row => {
                        if (row !== editedRow) {
                            row.update(updateData);
                            updatedRows.push(row);
                        }
                    });

                    // 记录操作后的状态
                    const afterState = selectedRows.map(row => ({
                        row: row,
                        data: {...row.getData()}
                    }));

                    // 添加到历史记录
                    addToHistory({
                        type: 'sync',
                        field: field,
                        value: newValue,
                        oldValue: oldValue,
                        beforeState: beforeState,
                        afterState: afterState,
                        affectedRows: selectedRows.length
                    });
                } else {
                    // 单行编辑，只记录当前行的变化
                    addToHistory({
                        type: 'edit',
                        row: editedRow,
                        field: field,
                        oldValue: oldValue,
                        newValue: newValue
                    });
                }

                // 清除当前行的计算结果，重置状态（如果不是计算结果字段）
                if (!['calcDiameter', 'allowableStress', 'pipeSpec', 'status'].includes(field)) {
                    editedRow.update({
                        calcDiameter: null,
                        allowableStress: null,
                        pipeSpec: null,
                        status: "待计算"
                    });
                }
            });

            // 监听行选择变化
            pipeTable.on("rowSelectionChanged", function(data, rows) {
                updateBatchSetButton();
            });

            // 初始化批量设置模态框的复选框事件
            initializeBatchSetModal();

            // 初始化键盘快捷键
            initializeKeyboardShortcuts();

            // 更新撤销/重做按钮状态
            updateHistoryButtons();
        }

        // 初始化键盘快捷键
        function initializeKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl+A: 全选
                if (e.ctrlKey && e.key === 'a' && !e.target.matches('input, textarea, select')) {
                    e.preventDefault();
                    selectAllRows();
                }

                // Ctrl+D: 取消选择
                if (e.ctrlKey && e.key === 'd' && !e.target.matches('input, textarea, select')) {
                    e.preventDefault();
                    deselectAllRows();
                }

                // Ctrl+B: 批量设置
                if (e.ctrlKey && e.key === 'b' && !e.target.matches('input, textarea, select')) {
                    e.preventDefault();
                    showBatchSetModal();
                }

                // Delete: 删除选中行
                if (e.key === 'Delete' && !e.target.matches('input, textarea, select')) {
                    e.preventDefault();
                    deleteSelectedRows();
                }

                // Ctrl+Z: 撤销
                if (e.ctrlKey && e.key === 'z' && !e.target.matches('input, textarea, select')) {
                    e.preventDefault();
                    undoLastAction();
                }

                // Ctrl+Y: 重做
                if (e.ctrlKey && e.key === 'y' && !e.target.matches('input, textarea, select')) {
                    e.preventDefault();
                    redoLastAction();
                }
            });
        }

        // 删除选中的行
        function deleteSelectedRows() {
            const selectedRows = pipeTable.getSelectedRows();
            if (selectedRows.length === 0) {
                alert('请先选择要删除的行');
                return;
            }

            if (confirm(`确定要删除选中的 ${selectedRows.length} 行数据吗？`)) {
                selectedRows.forEach(row => {
                    row.delete();
                });
            }
        }

        // 添加新行
        function addRow() {
            pipeTable.addRow({
                id: rowIdCounter++,
                medium: "压缩空气",
                material: "304",
                pressure: 1.0,
                temperature: 20,
                flow: 5.0,
                velocity: 50.0,
                status: "待计算"
            });
        }

        // 删除行
        function deleteRow(index) {
            const row = pipeTable.getRowFromPosition(index + 1);
            if (row) {
                row.delete();
            }
        }

        // 计算单行
        function calculateSingleRow(index) {
            const row = pipeTable.getRowFromPosition(index + 1);
            if (!row) return;

            const data = row.getData();

            try {
                // 验证数据
                if (!data.medium || !data.material || !data.pressure || !data.temperature || !data.flow || !data.velocity) {
                    throw new Error("请填写完整的数据");
                }

                // 计算内径 (基于流量和推荐流速)
                // 公式: D = sqrt(4 * Q / (π * v * ρ))，这里简化为基于质量流量和流速
                const velocity = parseFloat(data.velocity) || 50; // m/s
                const flow = parseFloat(data.flow) || 5; // kg/s

                // 假设流体密度（简化计算）
                let density = 1.2; // kg/m³ (压缩空气默认)
                if (data.medium === '水') {
                    density = 1000;
                } else if (data.medium === '航空煤油') {
                    density = 800;
                }

                // 体积流量 = 质量流量 / 密度
                const volumeFlow = flow / density; // m³/s

                // 内径计算: D = sqrt(4 * Q / (π * v))
                const calcDiameter = Math.sqrt(4 * volumeFlow / (Math.PI * velocity)) * 1000; // 转换为mm

                // 计算许用应力
                let allowableStress = 0;
                try {
                    if (typeof getAllowableStress === 'function') {
                        allowableStress = getAllowableStress(data.material, data.temperature);
                    } else {
                        // 简化的许用应力计算
                        const stressData = {
                            '20G': { base: 137, tempFactor: 0.95 },
                            '16MnR': { base: 170, tempFactor: 0.92 },
                            '15CrMoR': { base: 170, tempFactor: 0.90 },
                            '12Cr1MoVR': { base: 170, tempFactor: 0.88 },
                            '321': { base: 137, tempFactor: 0.85 },
                            '304': { base: 137, tempFactor: 0.85 },
                            '316L': { base: 137, tempFactor: 0.85 }
                        };

                        const materialData = stressData[data.material] || stressData['304'];
                        const tempFactor = data.temperature > 200 ? materialData.tempFactor : 1.0;
                        allowableStress = materialData.base * tempFactor;
                    }
                } catch (error) {
                    console.warn('许用应力计算失败，使用默认值:', error);
                    allowableStress = 137; // 默认值
                }

                // 计算管道规格
                const nominalDiameter = Math.ceil(calcDiameter / 5) * 5; // 向上取整到5的倍数
                let pipeSpec = '';

                if (nominalDiameter <= 15) {
                    pipeSpec = 'DN15';
                } else if (nominalDiameter <= 20) {
                    pipeSpec = 'DN20';
                } else if (nominalDiameter <= 25) {
                    pipeSpec = 'DN25';
                } else if (nominalDiameter <= 32) {
                    pipeSpec = 'DN32';
                } else if (nominalDiameter <= 40) {
                    pipeSpec = 'DN40';
                } else if (nominalDiameter <= 50) {
                    pipeSpec = 'DN50';
                } else if (nominalDiameter <= 65) {
                    pipeSpec = 'DN65';
                } else if (nominalDiameter <= 80) {
                    pipeSpec = 'DN80';
                } else if (nominalDiameter <= 100) {
                    pipeSpec = 'DN100';
                } else if (nominalDiameter <= 125) {
                    pipeSpec = 'DN125';
                } else if (nominalDiameter <= 150) {
                    pipeSpec = 'DN150';
                } else if (nominalDiameter <= 200) {
                    pipeSpec = 'DN200';
                } else if (nominalDiameter <= 250) {
                    pipeSpec = 'DN250';
                } else if (nominalDiameter <= 300) {
                    pipeSpec = 'DN300';
                } else {
                    pipeSpec = `DN${Math.ceil(nominalDiameter / 50) * 50}`;
                }

                // 更新行数据
                row.update({
                    calcDiameter: parseFloat(calcDiameter.toFixed(2)),
                    allowableStress: parseFloat(allowableStress.toFixed(2)),
                    pipeSpec: pipeSpec,
                    status: "已计算"
                });

            } catch (error) {
                console.error('计算错误:', error);
                row.update({
                    status: "错误",
                    calcDiameter: null,
                    allowableStress: null,
                    pipeSpec: error.message
                });
            }
        }

        // 批量计算
        function batchCalculate() {
            const rows = pipeTable.getRows();
            let successCount = 0;
            let errorCount = 0;

            // 记录操作前的状态
            const beforeState = rows.map(row => ({
                row: row,
                data: {...row.getData()}
            }));

            rows.forEach((row, index) => {
                try {
                    const data = row.getData();

                    // 验证数据
                    if (!data.medium || !data.material || !data.pressure || !data.temperature || !data.flow || !data.velocity) {
                        throw new Error("请填写完整的数据");
                    }

                    // 计算内径 (基于流量和推荐流速)
                    const velocity = parseFloat(data.velocity) || 50; // m/s
                    const flow = parseFloat(data.flow) || 5; // kg/s

                    // 假设流体密度（简化计算）
                    let density = 1.2; // kg/m³ (压缩空气默认)
                    if (data.medium === '水') {
                        density = 1000;
                    } else if (data.medium === '航空煤油') {
                        density = 800;
                    }

                    // 体积流量 = 质量流量 / 密度
                    const volumeFlow = flow / density; // m³/s

                    // 内径计算: D = sqrt(4 * Q / (π * v))
                    const calcDiameter = Math.sqrt(4 * volumeFlow / (Math.PI * velocity)) * 1000; // 转换为mm

                    // 计算许用应力
                    const stressData = {
                        '20G': { base: 137, tempFactor: 0.95 },
                        '16MnR': { base: 170, tempFactor: 0.92 },
                        '15CrMoR': { base: 170, tempFactor: 0.90 },
                        '12Cr1MoVR': { base: 170, tempFactor: 0.88 },
                        '321': { base: 137, tempFactor: 0.85 },
                        '304': { base: 137, tempFactor: 0.85 },
                        '316L': { base: 137, tempFactor: 0.85 }
                    };

                    const materialData = stressData[data.material] || stressData['304'];
                    const tempFactor = data.temperature > 200 ? materialData.tempFactor : 1.0;
                    const allowableStress = materialData.base * tempFactor;

                    // 计算管道规格
                    const nominalDiameter = Math.ceil(calcDiameter / 5) * 5;
                    let pipeSpec = '';

                    if (nominalDiameter <= 15) {
                        pipeSpec = 'DN15';
                    } else if (nominalDiameter <= 20) {
                        pipeSpec = 'DN20';
                    } else if (nominalDiameter <= 25) {
                        pipeSpec = 'DN25';
                    } else if (nominalDiameter <= 32) {
                        pipeSpec = 'DN32';
                    } else if (nominalDiameter <= 40) {
                        pipeSpec = 'DN40';
                    } else if (nominalDiameter <= 50) {
                        pipeSpec = 'DN50';
                    } else if (nominalDiameter <= 65) {
                        pipeSpec = 'DN65';
                    } else if (nominalDiameter <= 80) {
                        pipeSpec = 'DN80';
                    } else if (nominalDiameter <= 100) {
                        pipeSpec = 'DN100';
                    } else if (nominalDiameter <= 125) {
                        pipeSpec = 'DN125';
                    } else if (nominalDiameter <= 150) {
                        pipeSpec = 'DN150';
                    } else if (nominalDiameter <= 200) {
                        pipeSpec = 'DN200';
                    } else if (nominalDiameter <= 250) {
                        pipeSpec = 'DN250';
                    } else if (nominalDiameter <= 300) {
                        pipeSpec = 'DN300';
                    } else {
                        pipeSpec = `DN${Math.ceil(nominalDiameter / 50) * 50}`;
                    }

                    // 更新行数据
                    row.update({
                        calcDiameter: parseFloat(calcDiameter.toFixed(2)),
                        allowableStress: parseFloat(allowableStress.toFixed(2)),
                        pipeSpec: pipeSpec,
                        status: "已计算"
                    });

                    successCount++;
                } catch (error) {
                    console.error('批量计算错误:', error);
                    row.update({
                        status: "错误",
                        calcDiameter: null,
                        allowableStress: null,
                        pipeSpec: error.message
                    });
                    errorCount++;
                }
            });

            // 记录操作后的状态
            const afterState = rows.map(row => ({
                row: row,
                data: {...row.getData()}
            }));

            // 添加到历史记录
            addToHistory({
                type: 'batchCalculate',
                beforeState: beforeState,
                afterState: afterState,
                affectedRows: rows.length
            });

            alert(`批量计算完成！成功：${successCount}行，失败：${errorCount}行`);
        }





        // 全选行
        function selectAllRows() {
            pipeTable.selectRow();
        }

        // 取消选择所有行
        function deselectAllRows() {
            pipeTable.deselectRow();
        }

        // 更新批量设置按钮状态
        function updateBatchSetButton() {
            const selectedRows = pipeTable.getSelectedRows();
            const batchSetBtn = document.getElementById('batchSetBtn');

            if (selectedRows.length > 0) {
                batchSetBtn.disabled = false;
                batchSetBtn.textContent = `批量设置 (${selectedRows.length})`;
            } else {
                batchSetBtn.disabled = true;
                batchSetBtn.textContent = '批量设置';
            }
        }

        // 显示批量设置模态框
        function showBatchSetModal() {
            const selectedRows = pipeTable.getSelectedRows();
            if (selectedRows.length === 0) {
                alert('请先选择要设置的行');
                return;
            }

            document.getElementById('selectedRowCount').textContent = selectedRows.length;
            $('#batchSetModal').modal('show');
        }

        // 初始化批量设置模态框
        function initializeBatchSetModal() {
            // 为每个复选框添加事件监听器
            const checkboxes = ['setBatchMedium', 'setBatchMaterial', 'setBatchPressure', 'setBatchTemperature', 'setBatchFlow', 'setBatchVelocity'];
            const inputs = ['batchMedium', 'batchMaterial', 'batchPressure', 'batchTemperature', 'batchFlow', 'batchVelocity'];

            checkboxes.forEach((checkboxId, index) => {
                const checkbox = document.getElementById(checkboxId);
                const input = document.getElementById(inputs[index]);

                checkbox.addEventListener('change', function() {
                    input.disabled = !this.checked;
                    if (this.checked) {
                        input.focus();
                    }
                });
            });
        }

        // 应用批量设置
        function applyBatchSettings() {
            const selectedRows = pipeTable.getSelectedRows();
            if (selectedRows.length === 0) {
                alert('没有选择的行');
                return;
            }

            // 记录操作前的状态
            const beforeState = selectedRows.map(row => ({
                row: row,
                data: {...row.getData()}
            }));

            const updates = {};

            // 检查哪些参数需要批量设置
            if (document.getElementById('setBatchMedium').checked) {
                updates.medium = document.getElementById('batchMedium').value;
            }
            if (document.getElementById('setBatchMaterial').checked) {
                updates.material = document.getElementById('batchMaterial').value;
            }
            if (document.getElementById('setBatchPressure').checked) {
                updates.pressure = parseFloat(document.getElementById('batchPressure').value);
            }
            if (document.getElementById('setBatchTemperature').checked) {
                updates.temperature = parseInt(document.getElementById('batchTemperature').value);
            }
            if (document.getElementById('setBatchFlow').checked) {
                updates.flow = parseFloat(document.getElementById('batchFlow').value);
            }
            if (document.getElementById('setBatchVelocity').checked) {
                updates.velocity = parseFloat(document.getElementById('batchVelocity').value);
            }

            if (Object.keys(updates).length === 0) {
                alert('请至少选择一个参数进行设置');
                return;
            }

            // 清除计算结果，因为参数已更改
            updates.calcDiameter = null;
            updates.allowableStress = null;
            updates.pipeSpec = null;
            updates.status = "待计算";

            // 批量更新选中的行
            selectedRows.forEach(row => {
                row.update(updates);
            });

            // 记录操作后的状态
            const afterState = selectedRows.map(row => ({
                row: row,
                data: {...row.getData()}
            }));

            // 关闭模态框
            $('#batchSetModal').modal('hide');

            // 重置表单
            document.getElementById('batchSetForm').reset();
            const checkboxes = document.querySelectorAll('#batchSetModal input[type="checkbox"]');
            checkboxes.forEach(cb => {
                cb.checked = false;
                const inputId = cb.id.replace('set', '');
                const input = document.getElementById(inputId);
                if (input) input.disabled = true;
            });

            alert(`已成功设置 ${selectedRows.length} 行数据`);

            // 记录批量设置操作到历史
            addToHistory({
                type: 'batchSet',
                beforeState: beforeState,
                afterState: afterState,
                affectedRows: selectedRows.length,
                updates: updates
            });
        }

        // 获取字段的显示名称
        function getFieldDisplayName(field) {
            const fieldNames = {
                'medium': '流体介质',
                'material': '管道材质',
                'pressure': '压力',
                'temperature': '温度',
                'flow': '流量',
                'velocity': '推荐流速'
            };
            return fieldNames[field] || field;
        }



        // 从第一个选中行获取值填充到批量设置表单
        function fillFromFirstSelected() {
            const selectedRows = pipeTable.getSelectedRows();
            if (selectedRows.length === 0) {
                alert('没有选中的行');
                return;
            }

            const firstRowData = selectedRows[0].getData();

            // 填充表单值
            document.getElementById('batchMedium').value = firstRowData.medium || '压缩空气';
            document.getElementById('batchMaterial').value = firstRowData.material || '304';
            document.getElementById('batchPressure').value = firstRowData.pressure || 1.0;
            document.getElementById('batchTemperature').value = firstRowData.temperature || 20;
            document.getElementById('batchFlow').value = firstRowData.flow || 5.0;
            document.getElementById('batchVelocity').value = firstRowData.velocity || 50.0;

            // 提示用户
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show mt-2';
            alert.innerHTML = `
                <i class="fas fa-check"></i>
                已从第一行获取参数值，请勾选需要批量设置的参数。
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            `;

            const modalBody = document.querySelector('#batchSetModal .modal-body');
            const existingAlert = modalBody.querySelector('.alert-success');
            if (existingAlert) {
                existingAlert.remove();
            }
            modalBody.appendChild(alert);

            // 3秒后自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }

        // 从第一个选中行获取值填充到批量设置表单
        function fillFromFirstSelected() {
            const selectedRows = pipeTable.getSelectedRows();
            if (selectedRows.length === 0) {
                alert('没有选中的行');
                return;
            }

            const firstRowData = selectedRows[0].getData();

            // 填充表单值
            document.getElementById('batchMedium').value = firstRowData.medium || '压缩空气';
            document.getElementById('batchMaterial').value = firstRowData.material || '304';
            document.getElementById('batchPressure').value = firstRowData.pressure || 1.0;
            document.getElementById('batchTemperature').value = firstRowData.temperature || 20;
            document.getElementById('batchFlow').value = firstRowData.flow || 5.0;
            document.getElementById('batchVelocity').value = firstRowData.velocity || 50.0;

            // 提示用户
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show mt-2';
            alert.innerHTML = `
                <i class="fas fa-check"></i>
                已从第一行获取参数值，请勾选需要批量设置的参数。
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            `;

            const modalBody = document.querySelector('#batchSetModal .modal-body');
            const existingAlert = modalBody.querySelector('.alert-success');
            if (existingAlert) {
                existingAlert.remove();
            }
            modalBody.appendChild(alert);

            // 3秒后自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }

        // CSV导入功能
        function importCSVData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const csv = e.target.result;
                    const lines = csv.split('\n');
                    const headers = lines[0].split(',').map(h => h.trim());

                    // 验证CSV格式
                    const requiredHeaders = ['流体介质', '管道材质', '压力', '温度', '流量', '推荐流速'];
                    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

                    if (missingHeaders.length > 0) {
                        alert(`CSV文件缺少必要的列：${missingHeaders.join(', ')}`);
                        return;
                    }

                    // 解析数据
                    const importData = [];
                    for (let i = 1; i < lines.length; i++) {
                        const line = lines[i].trim();
                        if (!line) continue;

                        const values = line.split(',').map(v => v.trim());
                        const rowData = {
                            id: rowIdCounter++,
                            medium: values[headers.indexOf('流体介质')] || "压缩空气",
                            material: values[headers.indexOf('管道材质')] || "304",
                            pressure: parseFloat(values[headers.indexOf('压力')]) || 1.0,
                            temperature: parseInt(values[headers.indexOf('温度')]) || 20,
                            flow: parseFloat(values[headers.indexOf('流量')]) || 5.0,
                            velocity: parseFloat(values[headers.indexOf('推荐流速')]) || 50.0,
                            status: "待计算"
                        };
                        importData.push(rowData);
                    }

                    // 添加到表格
                    pipeTable.addData(importData);
                    alert(`成功导入 ${importData.length} 行数据`);

                } catch (error) {
                    alert('CSV文件格式错误：' + error.message);
                }
            };
            reader.readAsText(file);
        }

        // 下载CSV模板
        function downloadCSVTemplate() {
            const csvContent = `流体介质,管道材质,压力,温度,流量,推荐流速
压缩空气,304,1.0,20,5.0,50.0
水,316L,0.8,60,10.0,3.0
航空煤油,20G,1.5,80,8.0,3.0
压缩空气,321,2.0,150,15.0,50.0
水,304,1.2,40,12.0,3.0`;

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', '管道数据模板.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Excel导出功能
        function exportToExcel() {
            const data = pipeTable.getData();
            if (data.length === 0) {
                alert('没有数据可导出');
                return;
            }

            // 准备基础数据工作表
            const basicData = data.map((row, index) => ({
                '序号': index + 1,
                '流体介质': row.medium,
                '管道材质': row.material,
                '压力(MPa)': row.pressure,
                '温度(°C)': row.temperature,
                '流量(kg/s)': row.flow,
                '推荐流速(m/s)': row.velocity,
                '计算内径(mm)': row.calcDiameter || '',
                '许用应力(MPa)': row.allowableStress || '',
                '管道规格': row.pipeSpec || '',
                '计算状态': row.status
            }));

            // 准备详细计算数据工作表
            const detailedData = data.filter(row => row.status === '已计算').map((row, index) => {
                const materialRange = getMaterialTemperatureRange(row.material);
                const tempStatus = row.temperature < materialRange.min ? '低于最低温度' :
                                 row.temperature > materialRange.max ? '高于最高温度' : '正常范围';

                return {
                    '序号': index + 1,
                    '流体介质': row.medium,
                    '管道材质': row.material,
                    '材质温度范围': `${materialRange.min}°C ~ ${materialRange.max}°C`,
                    '设计压力(MPa)': row.pressure,
                    '设计温度(°C)': row.temperature,
                    '温度状态': tempStatus,
                    '质量流量(kg/s)': row.flow,
                    '推荐流速(m/s)': row.velocity,
                    '计算内径(mm)': row.calcDiameter,
                    '许用应力(MPa)': row.allowableStress,
                    '管道规格': row.pipeSpec,
                    '计算时间': new Date().toLocaleString()
                };
            });

            // 准备统计数据工作表
            const statistics = [
                { '项目': '总行数', '数值': data.length },
                { '项目': '已计算行数', '数值': data.filter(row => row.status === '已计算').length },
                { '项目': '错误行数', '数值': data.filter(row => row.status === '错误').length },
                { '项目': '总流量(kg/s)', '数值': data.reduce((sum, row) => sum + (parseFloat(row.flow) || 0), 0).toFixed(2) },
                { '项目': '平均压力(MPa)', '数值': (data.reduce((sum, row) => sum + (parseFloat(row.pressure) || 0), 0) / data.length).toFixed(2) },
                { '项目': '平均温度(°C)', '数值': (data.reduce((sum, row) => sum + (parseFloat(row.temperature) || 0), 0) / data.length).toFixed(1) },
                { '项目': '导出时间', '数值': new Date().toLocaleString() }
            ];

            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 添加基础数据工作表
            const ws1 = XLSX.utils.json_to_sheet(basicData);
            ws1['!cols'] = [
                {wch: 6}, {wch: 12}, {wch: 12}, {wch: 12}, {wch: 12}, {wch: 12},
                {wch: 15}, {wch: 15}, {wch: 15}, {wch: 12}, {wch: 10}
            ];
            XLSX.utils.book_append_sheet(wb, ws1, "基础数据");

            // 添加详细计算工作表
            if (detailedData.length > 0) {
                const ws2 = XLSX.utils.json_to_sheet(detailedData);
                ws2['!cols'] = [
                    {wch: 6}, {wch: 12}, {wch: 12}, {wch: 20}, {wch: 12}, {wch: 12},
                    {wch: 12}, {wch: 12}, {wch: 15}, {wch: 15}, {wch: 15}, {wch: 12}, {wch: 20}
                ];
                XLSX.utils.book_append_sheet(wb, ws2, "详细计算");
            }

            // 添加统计数据工作表
            const ws3 = XLSX.utils.json_to_sheet(statistics);
            ws3['!cols'] = [{wch: 20}, {wch: 15}];
            XLSX.utils.book_append_sheet(wb, ws3, "统计信息");

            // 下载文件
            const fileName = `管道计算详细报告_${new Date().toISOString().slice(0, 10)}.xlsx`;
            XLSX.writeFile(wb, fileName);

            alert(`Excel报告已导出！包含${data.length}行数据，其中${detailedData.length}行已完成计算。`);
        }

        // 历史记录管理函数
        function addToHistory(action) {
            // 如果当前不在历史记录的末尾，删除后面的记录
            if (historyIndex < historyStack.length - 1) {
                historyStack = historyStack.slice(0, historyIndex + 1);
            }

            // 添加新的操作到历史记录
            historyStack.push(action);
            historyIndex++;

            // 限制历史记录大小
            if (historyStack.length > maxHistorySize) {
                historyStack.shift();
                historyIndex--;
            }

            // 更新按钮状态
            updateHistoryButtons();
        }

        function updateHistoryButtons() {
            const undoBtn = document.getElementById('undoBtn');
            const redoBtn = document.getElementById('redoBtn');

            undoBtn.disabled = historyIndex < 0;
            redoBtn.disabled = historyIndex >= historyStack.length - 1;

            // 更新按钮文本显示操作类型
            if (historyIndex >= 0 && historyStack[historyIndex]) {
                const action = historyStack[historyIndex];
                const actionName = getActionDisplayName(action.type);
                undoBtn.innerHTML = `<i class="fas fa-undo"></i> 撤销${actionName}`;
            } else {
                undoBtn.innerHTML = `<i class="fas fa-undo"></i> 撤销`;
            }

            if (historyIndex < historyStack.length - 1 && historyStack[historyIndex + 1]) {
                const action = historyStack[historyIndex + 1];
                const actionName = getActionDisplayName(action.type);
                redoBtn.innerHTML = `<i class="fas fa-redo"></i> 重做${actionName}`;
            } else {
                redoBtn.innerHTML = `<i class="fas fa-redo"></i> 重做`;
            }
        }

        function getActionDisplayName(type) {
            const names = {
                'edit': '编辑',
                'sync': '同步',
                'batchSet': '批量设置',
                'delete': '删除',
                'add': '添加'
            };
            return names[type] || '';
        }

        function undoLastAction() {
            if (historyIndex < 0) return;

            const action = historyStack[historyIndex];

            switch (action.type) {
                case 'edit':
                    // 撤销单行编辑
                    action.row.update({[action.field]: action.oldValue});
                    break;

                case 'sync':
                case 'batchSet':
                    // 撤销批量操作
                    action.beforeState.forEach(state => {
                        state.row.update(state.data);
                    });
                    break;
            }

            historyIndex--;
            updateHistoryButtons();

            showUndoRedoNotification('撤销', getActionDisplayName(action.type));
        }

        function redoLastAction() {
            if (historyIndex >= historyStack.length - 1) return;

            historyIndex++;
            const action = historyStack[historyIndex];

            switch (action.type) {
                case 'edit':
                    // 重做单行编辑
                    action.row.update({[action.field]: action.newValue});
                    break;

                case 'sync':
                case 'batchSet':
                    // 重做批量操作
                    action.afterState.forEach(state => {
                        state.row.update(state.data);
                    });
                    break;
            }

            updateHistoryButtons();

            showUndoRedoNotification('重做', getActionDisplayName(action.type));
        }

        function showUndoRedoNotification(actionType, operationType) {
            const notification = document.createElement('div');
            notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 250px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            notification.innerHTML = `
                <i class="fas fa-${actionType === '撤销' ? 'undo' : 'redo'}"></i>
                <strong>${actionType}成功！</strong> ${operationType}操作已${actionType}。
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    $(notification).alert('close');
                }
            }, 2000);
        }
    </script>
</body>

</html>