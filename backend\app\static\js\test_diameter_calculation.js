// 内径计算功能测试脚本
// 验证修正后的内径计算公式是否正确

/**
 * 测试用例数据
 */
const testCases = [
    // 气体测试用例
    {
        name: '压缩空气测试1',
        medium: '压缩空气',
        pressure: 1000000, // 1 MPa = 1000000 Pa
        temperature: 293.15, // 20°C = 293.15 K
        flow_kg_s: 0.1, // 0.1 kg/s
        expected_diameter_range: [15, 25] // 预期内径范围 mm
    },
    {
        name: '压缩空气测试2',
        medium: '压缩空气',
        pressure: 2000000, // 2 MPa
        temperature: 373.15, // 100°C = 373.15 K
        flow_kg_s: 0.5, // 0.5 kg/s
        expected_diameter_range: [25, 40] // 预期内径范围 mm
    },
    
    // 液体测试用例
    {
        name: '水测试1',
        medium: '水',
        pressure: 1000000, // 1 MPa (对液体计算影响不大)
        temperature: 293.15, // 20°C
        flow_kg_s: 5, // 5 kg/s
        expected_diameter_range: [45, 55] // 预期内径范围 mm
    },
    {
        name: '航空煤油测试1',
        medium: '航空煤油',
        pressure: 1000000, // 1 MPa
        temperature: 293.15, // 20°C
        flow_kg_s: 2, // 2 kg/s
        expected_diameter_range: [30, 40] // 预期内径范围 mm
    }
];

/**
 * 手动计算验证函数
 */
function manualCalculation(testCase) {
    const { medium, pressure, temperature, flow_kg_s } = testCase;
    const config = MEDIUM_CONFIG[medium];
    
    console.log(`\n=== 手动计算验证: ${testCase.name} ===`);
    console.log(`介质: ${medium}`);
    console.log(`压力: ${pressure} Pa`);
    console.log(`温度: ${temperature} K`);
    console.log(`质量流量: ${flow_kg_s} kg/s`);
    
    let density, velocity;
    
    if (config.type === 'gas') {
        // 气体密度计算: ρ = P / (R * T)
        const Rg = config.Rg;
        density = pressure / (Rg * temperature);
        velocity = config.velocity;
        
        console.log(`气体常数 Rg: ${Rg} J/(kg·K)`);
        console.log(`计算密度: ${density.toFixed(3)} kg/m³`);
    } else {
        density = config.density;
        velocity = config.velocity;
        console.log(`液体密度: ${density} kg/m³`);
    }
    
    console.log(`推荐流速: ${velocity} m/s`);
    
    // 体积流量计算
    const Q_v = flow_kg_s / density;
    console.log(`体积流量: ${Q_v.toFixed(6)} m³/s`);
    
    // 内径计算: d = sqrt((4 * Q_v) / (π * v))
    const d_m = Math.sqrt((4 * Q_v) / (Math.PI * velocity));
    const d_mm = d_m * 1000;
    
    console.log(`计算内径: ${d_mm.toFixed(2)} mm`);
    
    return {
        density: density,
        volumeFlow: Q_v,
        diameter_mm: d_mm
    };
}

/**
 * 运行内径计算测试
 */
function runDiameterCalculationTests() {
    console.log('=== 内径计算功能测试开始 ===');
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    testCases.forEach((testCase, index) => {
        console.log(`\n--- 测试 ${index + 1}: ${testCase.name} ---`);
        
        try {
            // 1. 先进行单位换算
            const std = convertUnits({
                pressure: testCase.pressure / 1e6, // 转换为 MPa
                pressureUnit: 'MPa',
                temperature: testCase.temperature - 273.15, // 转换为 °C
                temperatureUnit: 'C',
                flow: testCase.flow_kg_s,
                flowUnit: 'kg/s',
                medium: testCase.medium
            });
            
            console.log('单位换算结果:');
            console.log(`  压力: ${std.pressure} Pa`);
            console.log(`  温度: ${std.temperature} K`);
            console.log(`  流量: ${std.flow} kg/s`);
            
            // 2. 调用修正后的内径计算函数
            const calculatedDiameter = parseFloat(calculatePipeInnerDiameter({
                medium: testCase.medium,
                flow_kg_s: std.flow
            }));
            
            console.log(`系统计算内径: ${calculatedDiameter} mm`);
            
            // 3. 手动验证计算
            const manualResult = manualCalculation({
                ...testCase,
                pressure: std.pressure,
                temperature: std.temperature,
                flow_kg_s: std.flow
            });
            
            // 4. 比较结果
            const difference = Math.abs(calculatedDiameter - manualResult.diameter_mm);
            const tolerance = 0.01; // 允许0.01mm的误差
            
            if (difference <= tolerance) {
                console.log(`✓ 计算结果一致，误差: ${difference.toFixed(4)} mm`);
                
                // 检查是否在预期范围内
                const [minExpected, maxExpected] = testCase.expected_diameter_range;
                if (calculatedDiameter >= minExpected && calculatedDiameter <= maxExpected) {
                    console.log(`✓ 结果在预期范围内 [${minExpected}, ${maxExpected}] mm`);
                    passedTests++;
                } else {
                    console.log(`⚠ 结果超出预期范围 [${minExpected}, ${maxExpected}] mm`);
                }
            } else {
                console.error(`✗ 计算结果不一致，误差: ${difference.toFixed(4)} mm`);
                console.error(`  系统计算: ${calculatedDiameter} mm`);
                console.error(`  手动计算: ${manualResult.diameter_mm.toFixed(2)} mm`);
            }
            
        } catch (error) {
            console.error(`✗ 测试执行错误: ${error.message}`);
        }
    });
    
    console.log(`\n=== 测试结果: ${passedTests}/${totalTests} 通过 ===`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！内径计算公式修正成功。');
    } else {
        console.log('❌ 部分测试失败，请检查计算逻辑。');
    }
}

/**
 * 对比修正前后的计算结果
 */
function compareOldAndNewFormulas() {
    console.log('\n=== 修正前后公式对比 ===');
    
    const testCase = {
        medium: '压缩空气',
        pressure: 1000000, // 1 MPa
        temperature: 293.15, // 20°C
        flow_kg_s: 0.1
    };
    
    console.log('测试条件:');
    console.log(`介质: ${testCase.medium}`);
    console.log(`压力: ${testCase.pressure / 1e6} MPa`);
    console.log(`温度: ${testCase.temperature - 273.15}°C`);
    console.log(`质量流量: ${testCase.flow_kg_s} kg/s`);
    
    // 计算气体密度
    const config = MEDIUM_CONFIG[testCase.medium];
    const density = testCase.pressure / (config.Rg * testCase.temperature);
    
    console.log(`\n计算参数:`);
    console.log(`气体常数 Rg: ${config.Rg} J/(kg·K)`);
    console.log(`密度: ${density.toFixed(3)} kg/m³`);
    console.log(`推荐流速: ${config.velocity} m/s`);
    
    // 修正前的错误公式
    const oldFormula = Math.sqrt((testCase.flow_kg_s * config.Rg * testCase.temperature) / (testCase.pressure * Math.PI / 4));
    const oldDiameter = oldFormula * 1000;
    
    // 修正后的正确公式
    const Q_v = testCase.flow_kg_s / density;
    const newFormula = Math.sqrt((4 * Q_v) / (Math.PI * config.velocity));
    const newDiameter = newFormula * 1000;
    
    console.log(`\n公式对比:`);
    console.log(`修正前公式: d = sqrt((m * Rg * T) / (P * π/4))`);
    console.log(`修正前结果: ${oldDiameter.toFixed(2)} mm`);
    console.log(`修正后公式: d = sqrt((4 * Q_v) / (π * v))`);
    console.log(`修正后结果: ${newDiameter.toFixed(2)} mm`);
    console.log(`差异: ${Math.abs(newDiameter - oldDiameter).toFixed(2)} mm`);
    
    // 分析差异原因
    console.log(`\n差异分析:`);
    console.log(`修正前公式没有考虑推荐流速，导致计算结果偏差`);
    console.log(`修正后公式基于连续性方程，考虑了实际流速要求`);
}

// 如果在浏览器环境中运行，将函数添加到全局作用域
if (typeof window !== 'undefined') {
    window.runDiameterCalculationTests = runDiameterCalculationTests;
    window.compareOldAndNewFormulas = compareOldAndNewFormulas;
    window.manualCalculation = manualCalculation;
}
